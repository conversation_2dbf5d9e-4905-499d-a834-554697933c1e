import Cookies from "js-cookie";
import { ACCESS_TOKEN, REFRESH_TOKEN } from "../Constant";

/**
 * Utility functions for token management across the application
 */

/**
 * Get access token from cookies or localStorage
 */
export const getAccessToken = (): string | null => {
  return Cookies.get(ACCESS_TOKEN) || localStorage.getItem(ACCESS_TOKEN);
};

/**
 * Get refresh token from cookies or localStorage
 */
export const getRefreshToken = (): string | null => {
  return Cookies.get(REFRESH_TOKEN) || localStorage.getItem(REFRESH_TOKEN);
};

/**
 * Set tokens in both cookies and localStorage for cross-domain compatibility
 */
export const setTokens = (accessToken: string, refreshToken?: string) => {
  const currentDomain = window.location.hostname;
  const isLocalhost = currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1');
  
  // Cookie options for cross-domain sharing
  const cookieOptions = {
    sameSite: "Strict" as const,
    secure: !isLocalhost,
    expires: 0.125, // 3 hours
    path: '/',
    ...(isLocalhost ? {} : { 
      domain: currentDomain.includes('.') ? 
        '.' + currentDomain.split('.').slice(-2).join('.') : 
        currentDomain 
    })
  };

  // Set in cookies
  Cookies.set(ACCESS_TOKEN, accessToken, cookieOptions);
  if (refreshToken) {
    Cookies.set(REFRESH_TOKEN, refreshToken, cookieOptions);
  }

  // Set in localStorage as backup
  try {
    localStorage.setItem(ACCESS_TOKEN, accessToken);
    if (refreshToken) {
      localStorage.setItem(REFRESH_TOKEN, refreshToken);
    }
  } catch (error) {
    console.warn("Failed to store tokens in localStorage:", error);
  }
};

/**
 * Clear all tokens from both cookies and localStorage
 */
export const clearTokens = () => {
  // Clear cookies
  Cookies.remove(ACCESS_TOKEN);
  Cookies.remove(REFRESH_TOKEN);
  
  // Clear localStorage
  try {
    localStorage.removeItem(ACCESS_TOKEN);
    localStorage.removeItem(REFRESH_TOKEN);
  } catch (error) {
    console.warn("Failed to clear tokens from localStorage:", error);
  }
};

/**
 * Check if user is authenticated (has valid tokens)
 */
export const isAuthenticated = (): boolean => {
  const accessToken = getAccessToken();
  const refreshToken = getRefreshToken();
  return !!(accessToken && refreshToken);
};
