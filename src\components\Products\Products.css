body {
  margin: 0px;
}

.container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background: white;
  border-radius: 24px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 24px;
}

.header {
  align-self: stretch;
  border-radius: 24px;
  overflow: hidden;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  color: #ff8732;
  font-size: 18px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 28px;
  word-wrap: break-word;
}

.search-bar {
  width: 297px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-input {
  width: 228px;
  height: 40px;
  background: white !important;
  border-radius: 20px;
  border: 1px solid #d6d5d5 !important;
}

.search-icon {
  width: 18px;
  height: 18px;
  position: relative;
}

.search-circle {
  width: 15.37px;
  height: 15.38px;
  left: 0.94px;
  top: 0.94px;
  position: absolute;
  background: #bcbcbc;
}

.search-dot {
  width: 2.62px;
  height: 2.62px;
  left: 14.44px;
  top: 14.44px;
  position: absolute;
  background: #bcbcbc;
}

.search-overlay {
  width: 18px;
  height: 18px;
  left: 0;
  top: 0;
  position: absolute;
  opacity: 0;
  background: #bcbcbc;
}

.search-text {
  width: 100%;
  height: 18px;
  color: #bcbcbc;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  word-wrap: break-word;
}

.content {
  align-self: stretch;
  height: 267px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 15px;
}

.content-header {
  align-self: stretch;
  padding-top: 10px;
  padding-bottom: 10px;
  background: white;
  border-bottom: 1px solid #e7e7e7;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 93px;
}

.header-item {
  flex: 1 1 0;
  color: #502314;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  line-height: 24px;
  word-wrap: break-word;
}

.content-body {
  align-self: stretch;
  height: 208px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
}

.row {
  align-self: stretch;
  padding-top: 4px;
  padding-bottom: 4px;
  border-radius: 5px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 93px;
}

.row-item {
  flex: 1 1 0;
  color: #502314;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  line-height: 24px;
  word-wrap: break-word;
}

.row-item:nth-child(2),
.row-item:nth-child(3) {
  color: #848484;
  font-weight: 400;
}
.table {
  display: grid;
  grid-template-columns: 1fr 2fr 2fr; /* Adjust column widths as needed */
  gap: 0; /* No gap between rows and columns */
  width: 100%;
  border-collapse: collapse;
}

.table-header,
.row {
  display: contents; /* Ensures children follow the grid layout */
}

.table-cell {
  padding: 8px 16px;
  border: 1px solid #e7e7e7;
  text-align: left;
  font-size: 14px;
  font-family: "Poppins", sans-serif;
  line-height: 20px;
}

.table-cell-header {
  background-color: #f5f5f5; /* Light background for the header */
  font-weight: 600;
}

.table-cell-content {
  color: #555;
}

.row:hover {
  background-color: #f9f9f9; /* Highlight row on hover */
}

header {
  padding-left: 30px !important;
}
header {
  height: auto !important;
}
.logo-section-s {
  display: flex;
  align-items: center;
}
.ant-table-wrapper {
  margin-top: 30px;
}
.container.product-card-banner {
  box-shadow: none;
  border: 1px solid #f3f2f2;
  margin-top: 20px;
}
.ant-layout-sider-children {
  height: auto !important;
}
.ant-layout-sider-children {
  background: #ff8732;
  height: 86vh !important;
}
main.ant-layout-content {
  height: 100% !important;
  /* height: 100vh !important; */
  overflow: auto;
}

li.ant-menu-item.ant-menu-item-only-child {
  background: #ff700a;
  padding-left: 14px !important;
  margin: 0px;
  margin-left: 0px;
  width: 100%;
  border-radius: 0px;
}
ul.ant-menu.ant-menu-sub.ant-menu-inline {
  width: 80%;
  float: right;
  margin-right: 15px;
  border-radius: 15px !important;
  padding: 5px;
  background: #ff700a !important;
}
.searchcontent {
  color: black;
}

.ant-popover-content {
  position: relative;
  left: -80px;
}
.ant-popover-inner-content {
  height: 300px !important;
  overflow: scroll !important;
  overflow-x: hidden !important;
}
.button-serachs {
  display: flex;
  margin-right: 30px;
}

.button-serachs button {
  background: #ff8732;
  font-size: 16px;
  padding-left: 20px;
  padding-right: 20px;
  border: none;
  border-radius: 20px;
  color: white;
  margin-right: 10px;
  margin-left: 15px;
}

/* back button */

.back-button {
  background: #ff8732;
  color: #fff;
  height: 20px;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Poppins";
}
.anticon svg {
  height: 16px;
  width: 16px;
}

.back-button:hover {
  background: #ff8732 !important;
  color: #fff !important;
  padding-left: 20px;
  padding-right: 20px;
  font-family: "Poppins";
}

/* common card render in details page */
.order-details-value {
  font-family: "Poppins", serif !important;
}
.order-details-value {
  margin: 5px;
  padding: 10px;
  padding-left: 0px;
}
.order-details-value {
  display: flex;
}
.order-details-label {
  width: 80%;
}
.order-details-value .order-details-value-colon {
  width: 20px;
}
.order-details-value .order-details-value-value {
  width: 50%;
}
/* .ant-card-body {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
} */
.order-details-value {
  display: flex;
  /* width: 20%; */
  width: 100%;
}
.order-details-label {
  width: 20%;
}

/* edit upload button */

.edit-upload-button {
  position: absolute;
  top: -14px;
  right: -14px;
  padding: 12px;
  background-color: #ff8732 !important;
  color: #fff;
  border: none !important;
  border-radius: 50%;
  font-family: "Poppins";
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 20;
}
/* edit button for modal popup */
.edit-btn {
  margin-left: 8px;
  padding: 8px;
  background-color: #ff8732 !important;
  color: #fff;
  border: none !important;
  border-radius: 50%;
  font-family: "Poppins";
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.edit-btn:hover {
  background-color: #ff8732 !important;
  color: #fff !important;
}
