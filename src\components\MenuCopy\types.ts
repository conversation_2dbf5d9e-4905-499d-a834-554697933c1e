export interface StoreObject {
    name: string,
    id: number,
}

export interface AutoCompleteOptions {
    page: number,
    pageSize: number,
    data: StoreObject[],
    totalPages: number,
    search?:string
}

export interface getStoresParams {
    page: number,
    search?:string
}

export interface StoreDropdownProps {
    onChange: (value: StoreObject | null) => void,
    placeholder: string,
    [key: string]: any
}

export interface SubmitData {
    storeSrc: StoreObject,
    storeDest: StoreObject
}