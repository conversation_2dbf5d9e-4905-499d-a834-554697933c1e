import React, {
  useCallback,
  useEffect,
  useState,
  useMemo,
  startTransition,
  useRef,
} from "react";
import { Button, notification, Checkbox, message, Tag } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import BackButton from "../../../UI/BackButton";
import { useTableFilters } from "../../../../customHooks/useFilter";
import { ActiveStoreConfig, ActiveStoresResponse } from "../type";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";

const MapGroupToStores: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  // State variables
  const [storeOptions, setStoreOptions] = useState<ActiveStoreConfig[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [deselectedStores, setDeselectedStores] = useState<Set<number>>(
    new Set()
  );
  const [btnLoader, setBtnLoader] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(100);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [isSelectingAll, setIsSelectingAll] = useState<boolean>(false);
  const [allStoresCache, setAllStoresCache] = useState<
    ActiveStoreConfig[] | null
  >(null);
  const [initialCheckDone, setInitialCheckDone] = useState<boolean>(false);

  const isUpdatingRef = useRef<boolean>(false);
  const isMountedRef = useRef<boolean>(true);
  const requestAnimationFrameRef = useRef<number | null>(null);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const [searchValue, setSearchValue] = useState<string>("");

  // Component cleanup useEffect
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      // Cancel any pending requestAnimationFrame
      if (requestAnimationFrameRef.current) {
        cancelAnimationFrame(requestAnimationFrameRef.current);
        requestAnimationFrameRef.current = null;
      }
    };
  }, []);

  // Fetch stores on component mount or when page changes (after initial check)
  useEffect(() => {
    if (initialCheckDone) {
      fetchStores();
    }
  }, [currentPage, pageSize, memoizedFilters, initialCheckDone]);

  // Initial load trigger - run fetchStores on first mount regardless
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchStores();
    }, 100); // Small delay to allow initial setup

    return () => clearTimeout(timer);
  }, []); // Run only once on mount

  const fetchStores = async () => {
    setLoading(true);
    try {
      // Create an AbortController to cancel the request if component unmounts
      const abortController = new AbortController();
      const signal = abortController.signal;

      const response = await axiosInstance.get<ActiveStoresResponse>(
        `/api/stores/groups-stores-list/`,
        {
          params: {
            group_id: id,
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal, // Pass the signal to make the request cancellable
        }
      );

      // Check if component is still mounted before updating state
      if (!isMountedRef.current) return;

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: ActiveStoreConfig) => ({
            id: store.id,
            name: store.name,
            code: store.code,
            is_selected: store.is_selected,
          })
        );

        // Sort stores to display initially selected stores first
        const sortedStores = fetchedStores.sort((a, b) => {
          if (a.is_selected && !b.is_selected) return -1;
          if (!a.is_selected && b.is_selected) return 1;
          return 0;
        });

        // Check if component is still mounted before updating state
        if (!isMountedRef.current) return;

        setStoreOptions(sortedStores);
        setTotalCount(response.data.total_count);

        //  Capture initially selected stores
        const initiallySelected = new Set(
          fetchedStores
            .filter((store) => store.is_selected)
            .map((store: ActiveStoreConfig) => store.id)
        );

        // Check if component is still mounted before updating state
        if (!isMountedRef.current) return;

        // Update initial selected stores only on first load or when not preserving selections
        if (initialSelectedStores.size === 0 && !initialCheckDone) {
          setInitialSelectedStores(initiallySelected);
          // Merge initial selections with any newly selected stores
          setSelectedStores(
            new Set([...initiallySelected, ...newlySelectedStores])
          );
        } else if (!initialCheckDone) {
          // If initial check not done, merge with newly selected
          setSelectedStores(
            new Set([...initiallySelected, ...newlySelectedStores])
          );
        }
        // If initial check is done, don't override the selection state
      } else {
        // Check if component is still mounted before updating state
        if (!isMountedRef.current) return;

        setStoreOptions([]);
        // setTotalCount(0);
      }
    } catch (error: any) {
      // Only log and show error if component is still mounted and it's not an abort error
      if (isMountedRef.current && error?.name !== "AbortError") {
        console.error("Error fetching stores:", error);
        notification.error({
          message: "Error",
          description: "Failed to load stores.",
        });
      }
    } finally {
      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  // Optimized fetch all stores with caching for better performance
  const fetchAllStores = useCallback(async (): Promise<ActiveStoreConfig[]> => {
    // Return cached data if available
    if (allStoresCache) {
      return allStoresCache;
    }

    try {
      // Create an AbortController to cancel the request if component unmounts
      const abortController = new AbortController();
      const signal = abortController.signal;

      // Use a single API call with large page size for better performance
      const response = await axiosInstance.get<ActiveStoresResponse>(
        `/api/stores/groups-stores-list/`,
        {
          params: {
            group_id: id,
            page: 1,
            page_size: 9999,
            ...memoizedFilters,
          },
          signal, // Pass the signal to make the request cancellable
        }
      );

      // Check if component is still mounted before updating state
      if (!isMountedRef.current) {
        return [];
      }

      const allStores = response.data?.objects || [];
      // Cache the result for future use only if component is still mounted
      if (isMountedRef.current) {
        setAllStoresCache(allStores);
      }
      return allStores;
    } catch (error: any) {
      // Only log error if component is still mounted and it's not an abort error
      if (isMountedRef.current && error?.name !== "AbortError") {
        console.error("Error fetching all stores:", error);
      }
      throw error;
    } finally {
      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [id, memoizedFilters]);

  // Check if all stores are initially selected and set SelectAll accordingly
  const checkInitialSelectionStatus = useCallback(async () => {
    if (initialCheckDone) return;

    try {
      const allStores = await fetchAllStores();

      // Check if component is still mounted before continuing
      if (!isMountedRef.current) return;

      const allSelected = allStores.every((store) => store.is_selected);

      if (allSelected && allStores.length > 0) {
        // All stores are initially selected, so set them as selected
        const allStoreIds = new Set(allStores.map((store) => store.id));

        // Use startTransition for better performance
        startTransition(() => {
          setSelectedStores(allStoreIds);
          setInitialSelectedStores(allStoreIds);
          // Ensure total count is set correctly for SelectAll logic
          setTotalCount(allStores.length);
          // Immediately set allStoresSelected to true to show DeselectAll
          setAllStoresSelected(true);
          // Set total selected stores count to match total count
          setTotalSelectedStores(allStores.length);
        });

        message.success(
          `All ${allStores.length} stores are initially selected`
        );
      } else if (allStores.length > 0) {
        // Some stores are selected, set up initial state
        const initiallySelected = new Set(
          allStores
            .filter((store) => store.is_selected)
            .map((store) => store.id)
        );

        // Calculate initial selected count
        const initialSelectedCount = initiallySelected.size;

        startTransition(() => {
          setSelectedStores(initiallySelected);
          setInitialSelectedStores(initiallySelected);
          setTotalCount(allStores.length);
          // Set allStoresSelected based on whether all stores are selected
          setAllStoresSelected(initialSelectedCount === allStores.length);
          // Set total selected stores count immediately
          setTotalSelectedStores(initialSelectedCount);
        });
      }

      // Only update state if component is still mounted
      if (isMountedRef.current) {
        setInitialCheckDone(true);
      }
    } catch (error: any) {
      // Only log error and update state if component is still mounted and it's not an abort error
      if (isMountedRef.current && error?.name !== "AbortError") {
        console.error("Error checking initial selection status:", error);
        setInitialCheckDone(true);
      }
    }
  }, [initialCheckDone, fetchAllStores]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);

    setCurrentPage(parseInt(params.get("page") || "1", 10));
    setPageSize(parseInt(params.get("page_size") || "100", 10));
  }, [location.search]);

  // Check initial selection status on component mount
  useEffect(() => {
    if (!initialCheckDone) {
      checkInitialSelectionStatus();
    }
  }, [checkInitialSelectionStatus, initialCheckDone]);

  // Fallback to ensure fetchStores runs if initial check takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!initialCheckDone) {
        console.log(
          "Initial check taking too long, proceeding with fetchStores"
        );
        setInitialCheckDone(true);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timer);
  }, [initialCheckDone]);

  const handleRowCheckboxChange = useCallback(
    (store: ActiveStoreConfig, checked: boolean) => {
      // Prevent rapid successive updates
      if (isUpdatingRef.current) {
        return;
      }

      isUpdatingRef.current = true;

      // Use requestAnimationFrame for optimal performance
      requestAnimationFrameRef.current = requestAnimationFrame(() => {
        // Check if component is still mounted before updating state
        if (!isMountedRef.current) {
          isUpdatingRef.current = false;
          return;
        }

        // Batch all state updates in a single operation for better performance
        if (checked) {
          // Add store to selection - optimized updates
          setSelectedStores((prev) => {
            if (prev.has(store.id)) return prev; // Already selected
            const updated = new Set(prev);
            updated.add(store.id);
            return updated;
          });

          // If store was not initially selected (not in initialSelectedStores AND not is_creative_selected), add to newly selected
          if (!initialSelectedStores.has(store.id) && !store.is_selected) {
            setNewlySelectedStores((prev) => {
              if (prev.has(store.id)) return prev; // Already in newly selected
              const updated = new Set(prev);
              updated.add(store.id);
              return updated;
            });
          }

          setDeselectedStores((prev) => {
            if (!prev.has(store.id)) return prev; // Not in deselected
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });

          // Update total selected stores count immediately for better UI responsiveness
          setTotalSelectedStores((prev) => prev + 1);

          // Check if all stores are now selected after this change
          // This is an approximation - the useEffect will do a more accurate check later
          if (totalCount > 0 && totalSelectedStores + 1 >= totalCount) {
            setAllStoresSelected(true);
          }
        } else {
          // Remove store from selection - optimized updates
          setSelectedStores((prev) => {
            if (!prev.has(store.id)) return prev; // Already not selected
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });

          setNewlySelectedStores((prev) => {
            if (!prev.has(store.id)) return prev; // Not in newly selected
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });

          // If store was initially selected (either in initialSelectedStores OR has is_creative_selected: true), add to deselected
          if (initialSelectedStores.has(store.id) || store.is_selected) {
            setDeselectedStores((prev) => {
              if (prev.has(store.id)) return prev; // Already in deselected
              const updated = new Set(prev);
              updated.add(store.id);
              return updated;
            });
          }

          // Update total selected stores count immediately for better UI responsiveness
          setTotalSelectedStores((prev) => Math.max(0, prev - 1));

          // When deselecting any store, we know all stores are not selected
          setAllStoresSelected(false);
        }

        // Reset the updating flag
        isUpdatingRef.current = false;
      });
    },
    [initialSelectedStores]
  );

  // "Select All" across all pages with batched state updates and debouncing
  const handleSelectAllCheckbox = useCallback(
    async (checked: boolean) => {
      // Prevent multiple simultaneous operations
      if (isSelectingAll) {
        return;
      }

      setIsSelectingAll(true);
      try {
        if (checked) {
          // Use cached data if available to improve performance
          let allStores;

          if (allStoresCache && allStoresCache.length > 0) {
            allStores = allStoresCache;
          } else {
            // Fetch all stores across all pages only if cache is not available
            allStores = await fetchAllStores();
          }

          if (allStores.length === 0) {
            message.warning("No stores found to select");
            return;
          }

          // Batch calculate all state changes for better performance
          const allStoreIds = new Set(allStores.map((store) => store.id));
          const newlySelected = new Set<number>();
          const updatedDeselected = new Set<number>();

          // Calculate newly selected stores (not in initial selection and not already selected)
          allStores.forEach((store) => {
            if (!initialSelectedStores.has(store.id) && !store.is_selected) {
              newlySelected.add(store.id);
            }
          });

          // Batch update all states at once to minimize re-renders using startTransition
          startTransition(() => {
            setSelectedStores(allStoreIds);
            setNewlySelectedStores(newlySelected);
            setDeselectedStores(updatedDeselected);
            // Immediately update these states for UI responsiveness
            setAllStoresSelected(true);
            setTotalSelectedStores(allStores.length);
          });

          message.success(
            `Selected ${allStores.length} stores across all pages`
          );
        } else {
          // Deselect all stores - optimize by using cached data when available
          let allStores;

          // Use cached data if available to avoid slow API call
          if (allStoresCache && allStoresCache.length > 0) {
            allStores = allStoresCache;
          } else {
            // Only fetch if cache is not available
            allStores = await fetchAllStores();
          }

          // Find all stores that have is_creative_selected: true (these need to be added to deselectedStores)
          const initiallySelectedFromAPI = new Set(
            allStores
              .filter((store) => store.is_selected)
              .map((store) => store.id)
          );

          // Batch update all states at once using startTransition for better performance
          startTransition(() => {
            setSelectedStores(new Set());
            setNewlySelectedStores(new Set());
            setDeselectedStores(initiallySelectedFromAPI);
            // Immediately update these states for UI responsiveness
            setAllStoresSelected(false);
            setTotalSelectedStores(0);
          });

          message.success("Deselected all stores");
        }
      } catch (error) {
        console.error("Error in select all:", error);
        message.error(
          "Failed to select/deselect all stores. Please try again."
        );
      } finally {
        setIsSelectingAll(false);
      }
    },
    [fetchAllStores, initialSelectedStores, isSelectingAll]
  );

  // Clear all selections
  // const handleClearAll = useCallback(() => {
  //   setSelectedStores(new Set());
  //   setNewlySelectedStores(new Set());
  //   setDeselectedStores(new Set(initialSelectedStores));
  //   message.success("Cleared all selections");
  // }, [initialSelectedStores]);

  // Clear cache when filters change to ensure fresh data
  useEffect(() => {
    setAllStoresCache(null);
  }, [memoizedFilters]);

  // Clear cache when component unmounts
  useEffect(() => {
    return () => {
      setAllStoresCache(null);
    };
  }, []);

  const handleStoreMapping = async () => {
    if (!id) {
      notification.error({
        message: "Invalid Store Group ID",
        description: "No ID found for mapping.",
      });
      return;
    }

    // store_id: Includes newly selected stores (not part of initially selected stores)
    const finalStoreIds = Array.from(newlySelectedStores);

    // creative_removed_store_id: Includes initially selected stores that were later deselected
    const removedStoreIds = Array.from(deselectedStores);

    if (finalStoreIds.length === 0 && removedStoreIds.length === 0) {
      notification.warning({
        message: "No Changes Made",
        description:
          "Please select or deselect at least one store before saving.",
        placement: "topLeft",
      });
      return;
    }

    //Construct payload
    const payload = {
      group_id: Number(id),
      add_stores: finalStoreIds, // Only newly selected stores
      remove_stores: removedStoreIds, // Only initially selected & later deselected stores
    };

    try {
      setBtnLoader(true);
      const response = await axiosInstance.post(
        "/api/stores/groups-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
        navigate(`/store-group-details/${id}?tab=Group_Mapped_Stores`);
      }
    } catch (error) {
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select or Deselect at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setBtnLoader(false);
    }
  };

  // Function to calculate total selected stores across all pages
  const calculateTotalSelectedStores =
    useCallback(async (): Promise<number> => {
      try {
        const allStores = await fetchAllStores();

        // Count stores that are effectively selected:
        // 1. Initially selected (is_creative_selected=true) and not deselected
        // 2. Newly selected stores
        let totalSelected = 0;

        allStores.forEach((store) => {
          const isInitiallySelected = store.is_selected;
          const isDeselected = deselectedStores.has(store.id);
          const isNewlySelected = newlySelectedStores.has(store.id);

          // Store is effectively selected if:
          // - It was initially selected and not deselected, OR
          // - It was newly selected
          if ((isInitiallySelected && !isDeselected) || isNewlySelected) {
            totalSelected++;
          }
        });

        return totalSelected;
      } catch (error) {
        console.error("Error calculating total selected stores:", error);
        return 0;
      }
    }, [fetchAllStores, deselectedStores, newlySelectedStores]);

  // State for total selected stores across all pages
  const [totalSelectedStores, setTotalSelectedStores] = useState<number>(0);

  // State to track if all stores across all pages are selected
  const [allStoresSelected, setAllStoresSelected] = useState<boolean>(false);

  // Update allStoresSelected state when relevant state changes
  useEffect(() => {
    let isCancelled = false;

    const checkAllStoresSelected = async () => {
      try {
        // Use a more efficient approach - check if totalSelectedStores equals totalCount
        // This avoids the need for async calls in most cases
        if (totalCount > 0) {
          const newAllStoresSelected = totalSelectedStores === totalCount;

          // Only update if component is still mounted and state actually changed
          if (!isCancelled && isMountedRef.current) {
            setAllStoresSelected((prev) =>
              prev !== newAllStoresSelected ? newAllStoresSelected : prev
            );
          }
        } else {
          // Fallback to async calculation if totalCount is not available
          const allStores = await fetchAllStores();

          // Check if cancelled or unmounted before continuing
          if (isCancelled || !isMountedRef.current) return;

          const totalSelected = await calculateTotalSelectedStores();

          // Check if cancelled or unmounted before updating state
          if (isCancelled || !isMountedRef.current) return;

          const newAllStoresSelected =
            totalSelected === allStores.length && allStores.length > 0;

          setAllStoresSelected((prev) =>
            prev !== newAllStoresSelected ? newAllStoresSelected : prev
          );
        }
      } catch (error: any) {
        // Only log error if component is still mounted and it's not an abort error
        if (
          !isCancelled &&
          isMountedRef.current &&
          error?.name !== "AbortError"
        ) {
          console.error("Error checking if all stores are selected:", error);
          setAllStoresSelected(false);
        }
      }
    };

    // Only check if not in the middle of selecting all and initial check is done
    // This prevents unnecessary calls during initial loading
    if (!isSelectingAll && initialCheckDone) {
      checkAllStoresSelected();
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isCancelled = true;
    };
  }, [
    totalSelectedStores,
    totalCount,
    calculateTotalSelectedStores,
    fetchAllStores,
    isSelectingAll,
    initialCheckDone,
  ]);

  // Computed state for select all functionality - now uses allStoresSelected for global state
  const isAllSelected = useMemo(() => {
    // For the checkbox state, use the global allStoresSelected state
    return allStoresSelected;
  }, [allStoresSelected]);

  const isIndeterminate = useMemo(() => {
    if (totalCount === 0 || storeOptions.length === 0) return false;

    // Count visually checked stores
    const visuallyCheckedCount = storeOptions.filter(
      (store) =>
        selectedStores.has(store.id) ||
        (store.is_selected && !deselectedStores.has(store.id))
    ).length;

    // Show indeterminate if some but not all stores are visually checked
    return (
      visuallyCheckedCount > 0 && visuallyCheckedCount < storeOptions.length
    );
  }, [selectedStores, storeOptions, deselectedStores]);

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 100);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };
  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  // Memoized checkbox render function for better performance
  const renderCheckbox = useCallback(
    (_: any, record: ActiveStoreConfig) => {
      // Display checkbox as checked if:
      // 1. Store is in selectedStores (user selected it)
      // 2. Store has is_creative_selected: true AND is not in deselectedStores (initially selected and not deselected by user)
      const isChecked =
        selectedStores.has(record.id) ||
        (record.is_selected && !deselectedStores.has(record.id));

      return (
        <Checkbox
          checked={isChecked}
          onChange={(e) => handleRowCheckboxChange(record, e.target.checked)}
        />
      );
    },
    [selectedStores, handleRowCheckboxChange, deselectedStores]
  );

  const columns = useMemo(
    () => [
      {
        title: (
          <Checkbox
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={(e) => handleSelectAllCheckbox(e.target.checked)}
            disabled={isSelectingAll || loading}
          >
            {isSelectingAll
              ? "Processing..."
              : allStoresSelected
              ? "Deselect All"
              : "Select All"}
          </Checkbox>
        ),
        dataIndex: "checkbox",
        key: "checkbox",
        width: "15%",
        fixed: "left" as "left",
        render: renderCheckbox,
      },
      {
        title: "Store Name",
        dataIndex: "name",
        key: "name",
        width: "40%",
        render: (text: string, record: ActiveStoreConfig) =>
          text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/stores/${record.id}/`}
            >
              {text}
            </Link>
          ) : (
            "N/A"
          ),
      },
      {
        title: "Store Code",
        dataIndex: "code",
        key: "code",
        width: "30%",
      },
    ],
    [
      isAllSelected,
      isIndeterminate,
      handleSelectAllCheckbox,
      renderCheckbox,
      isSelectingAll,
      loading,
      allStoresSelected,
    ]
  );

  // Update total selected stores count when relevant state changes
  useEffect(() => {
    let isCancelled = false;

    const updateTotalSelectedCount = async () => {
      try {
        const count = await calculateTotalSelectedStores();

        // Only update if component is still mounted and not cancelled
        if (!isCancelled && isMountedRef.current) {
          setTotalSelectedStores((prev) => (prev !== count ? count : prev));
        }
      } catch (error: any) {
        // Only log error if component is still mounted and it's not an abort error
        if (
          !isCancelled &&
          isMountedRef.current &&
          error?.name !== "AbortError"
        ) {
          console.error("Error updating total selected count:", error);
        }
      }
    };

    // Only update if not in the middle of selecting all and initial check is done
    // This prevents unnecessary calls during initial loading
    if (!isSelectingAll && initialCheckDone) {
      updateTotalSelectedCount();
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isCancelled = true;
    };
  }, [
    calculateTotalSelectedStores,
    selectedStores,
    deselectedStores,
    newlySelectedStores,
    isSelectingAll,
    initialCheckDone,
  ]);

  return (
    <div>
      <BackButton />
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Store Mapping</div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
        </div>
        <div className="flex-shrink-0">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={btnLoader}
          >
            {btnLoader ? `Saving...` : `Save Selection`}
          </Button>
        </div>
      </div>

      <>
        {/* Selection Summary - Show when there are selections or changes */}
        {(totalSelectedStores > 0 ||
          newlySelectedStores.size > 0 ||
          deselectedStores.size > 0 ||
          isSelectingAll) && (
          <div className="d-flex justify-content-between align-items-center mb-3 mt-3">
            <div className="d-flex align-items-center gap-2">
              <Tag color="blue" className="px-3 py-1 fs-6 fw-bold rounded-pill">
                {isSelectingAll ? (
                  <>Processing...</>
                ) : (
                  <>
                    {totalSelectedStores} Store
                    {totalSelectedStores > 1 ? "s" : ""} Selected
                  </>
                )}
              </Tag>
              {!isSelectingAll && newlySelectedStores.size > 0 && (
                <Tag color="green" className="px-2 py-1 fs-6 rounded-pill">
                  +{newlySelectedStores.size} Added
                </Tag>
              )}
              {!isSelectingAll && deselectedStores.size > 0 && (
                <Tag color="red" className="px-2 py-1 fs-6 rounded-pill">
                  -{deselectedStores.size} Removed
                </Tag>
              )}
            </div>
            {/* <Button
              type="link"
              size="small"
              onClick={handleClearAll}
              className="text-danger"
              disabled={isSelectingAll}
            >
              Clear All
            </Button> */}
          </div>
        )}

        <div className="mt-3">
          <DataTable<ActiveStoreConfig>
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
            key={`table-${currentPage}-${pageSize}`}
          />
        </div>

        <div className="d-flex justify-content-end mt-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default MapGroupToStores;
