import { Al<PERSON>, But<PERSON> } from "antd";
import { useCallback } from "react";

interface ErrorFallbackProps {
  error: string | null;
  onClicked: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onClicked }) => {
  if (!error) return null;

  const handleClick = useCallback(() => {
    onClicked();
    error = null;
  }, [onClicked]);

  return (
    <div className="error-container">
      <Alert message="Error" description={error} type="error" showIcon />
      <div className="retry-button d-flex justify-content-center align-items-center mt-3">
        <Button type="primary" onClick={handleClick}>
          Retry
        </Button>
      </div>
    </div>
  );
};

export default ErrorFallback;
