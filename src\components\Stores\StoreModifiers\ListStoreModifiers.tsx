import { StoreLevelModifierProps } from "../../../types";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

import "../../Products/Products.css";

// import { useNavigate } from "react-router-dom";
// import { Typography } from "antd";

import { Modal, Pagination } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useEffect, useMemo, useState } from "react";
import { message } from "antd/lib";
import { useTableFilters } from "../../../customHooks/useFilter";
import DataTable from "../../UI/DataTable/DataTable";
import SearchBox from "../../UI/SearchBox";

// const { Link } = Typography;

export interface StoreModifiers {
  objects: StoreLevelModifierProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}
interface StoreModifierPropsID {
  id: string;
}

const ListStoreModifiers: React.FC<StoreModifierPropsID> = ({ id }) => {
  // const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<StoreLevelModifierProps[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const {
    currentPage,
    pageSize,

    handlePageChange,
    handleFilterChange,
    filters,
  } = useTableFilters();
  const [search, setSearch] = useState<string>("");
  const memoizedFilters = useMemo(() => filters, [filters]);

  const [refreshTrigger, setRefreshTrigger] = useState(0);
  useEffect(() => {
    fetchData();
  }, [refreshTrigger]);
  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `/api/menu/store-modifiers/${id}/`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );

      setData(response.data.objects);
      setTotalCount(response.data.total_count);
      setLoading(false);
    } catch (error) {
      message.error("Failed to load data");
    }
  };

  useEffect(() => {
    fetchData();
  }, [id, currentPage, pageSize, memoizedFilters]);

  type ToggleKey = "is_active" | "is_available";
  const getLabel = (key: ToggleKey): string => {
    switch (key) {
      case "is_active":
        return "Status";
      case "is_available":
        return "Availability";
        return "Price Sync";
      default:
        return key;
    }
  };

  const handleClear = () => {
    setSearch("");
    handleFilterChange("search", "");
  };

  const handleToggle = async (
    record: StoreLevelModifierProps,
    key: "is_active" | "is_available",
    newValue: boolean
  ) => {
    Modal.confirm({
      title: newValue ? `Enable ${getLabel(key)}` : `Disable ${getLabel(key)}`,
      content: `Are you sure you want to ${
        newValue ? "enable" : "disable"
      } this ${getLabel(key)}?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          await axiosInstance.patch(
            `/api/menu/store-modifiers/${id}/${record.id}/`,
            { [key]: newValue }
          );

          setData((prevData) =>
            prevData.map((item) =>
              Number(item.id) === Number(record.id)
                ? { ...item, [key]: newValue }
                : item
            )
          );

          message.success(`${getLabel(key)} updated`);
          setRefreshTrigger((prev) => prev + 1);
        } catch (error) {
          message.error("Failed to update");
        }
      },
    });
  };

  const columns = [
    {
      title: "Code",
      width: "15%",
      dataIndex: "code",
      key: "code",
      // fixed: "left" as "left",
      // render: (text: string, ) => (
      //   <span >
      //     {text}
      //   </span>
      // ),
    },
    {
      title: "Name",
      width: "15%",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Price",
      width: "15%",
      dataIndex: "price",
      key: "price",
      // render: (text: number, record: StoreLevelModifierProps) => (
      //   <span>
      //     {text}{" "}
      //     <EditOutlined
      //       onClick={() =>
      //         navigate(`/stores/store-modifer/update/${record.id}?store=${id}&name=${record.id}`)
      //       }
      //       style={{ cursor: "pointer", marginLeft: 5 }}
      //     />
      //   </span>
      // ),
    },

    {
      title: "Status",

      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive: boolean, record: StoreLevelModifierProps) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleToggle(record, "is_active", !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
    {
      title: "Available",

      dataIndex: "is_available",
      key: "is_available",
      width: "15%",
      render: (isActive: boolean, record: StoreLevelModifierProps) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleToggle(record, "is_available", !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons"></div>

      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">STORE Modfiers</div>
          {/* <div className="search-container">
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(search)}>Search</button>
            </div>
          </div> */}
          <div className="d-flex flex-wrap">
            <SearchBox
              value={search}
              onChange={(v) => setSearch(v)}
              onSearch={() => handleFilterChange("search", search)}
              onClear={() => handleClear()}
              placeholder="Enter Name"
            />
          </div>
        </div>
      </div>

      <div className="pt-4 mt-4">
        <DataTable
          loading={loading}
          rowKey="key"
          columns={columns}
          dataSource={data}
          pagination={false}
          scroll={{ x: "max-content" }}
        />
      </div>
      <div className="d-flex justify-content-end mt-4">
        <Pagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          showSizeChanger={true}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default ListStoreModifiers;
