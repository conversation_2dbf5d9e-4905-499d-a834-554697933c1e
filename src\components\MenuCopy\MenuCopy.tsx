import { useState } from "react";
import StoresDropdown from "./StoresDropdown";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { Button, message, Spin } from "antd";
import { SubmitData } from "./types";
import { axiosInstance } from "../../apiCalls";

export default function MenuCopy() {
    const { control, handleSubmit } = useForm<SubmitData>();
    const [isPending, setIsPending] = useState(false);
    const onSubmit: SubmitHandler<SubmitData> = async (data) => {
        if (data?.storeSrc?.id === data?.storeDest?.id) {
            message.error("Source and Destination Store Cannot be Same");
        } else {
            try {
                setIsPending(true);
                await axiosInstance.post('api/menu/menu-cloning/', {
                    old_store_id: data?.storeSrc?.id,
                    new_store_id: data?.storeDest?.id
                })
                message.success("Menu Copy Job Started Successfully");
            } catch (err) {
                console.log(err);
                message.error("Menu Copy Job Starting Error");
            } finally {
                setIsPending(false);
            }
        }
    }
    return (
        <div style={{ display: 'flex', gap: '9px' }}>
            <Controller
                control={control}
                name={'storeSrc'}
                rules={{ required: { value: true, message: "Source Store Required" } }}
                render={({ field, formState: { errors: { storeSrc } } }) => {
                    return (
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                            <StoresDropdown
                                {...field}
                                placeholder={'select source store'}
                                onChange={(value) => field?.onChange(value)}
                                disabled={isPending}
                            />
                            {storeSrc && <p style={{ color: "red" }}>{storeSrc.message + ''}</p>}
                        </div>
                    );
                }}
            />
            <Controller
                control={control}
                name={'storeDest'}
                rules={{ required: { value: true, message: "Destination Store Required" } }}
                render={({ field, formState: { errors: { storeDest } } }) => {
                    return (
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                            <StoresDropdown
                                {...field}
                                placeholder={'select destination store'}
                                onChange={(value) => field?.onChange(value)}
                                disabled={isPending}
                            />
                            {storeDest && <p style={{ color: "red" }}>{storeDest.message + ''}</p>}
                        </div>
                    );
                }}
            />
            <Button style={{ minWidth: "100px" }} onClick={handleSubmit(onSubmit)} disabled={isPending}>
                {isPending
                    ? <Spin size="small" />
                    : "Copy Menu"
                }
            </Button>
        </div>
    );
}