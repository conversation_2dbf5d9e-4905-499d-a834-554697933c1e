import { ProductVariantModiferGroupProps } from "../../../../types/Products";
import { Typography } from "antd";
import { useParams } from "react-router-dom";
import StoreTabs from "../../../../reusableComponents/store/StoreTabs";
const { Link } = Typography;

const VariantModiferGroup = () => {
  // const navigate = useNavigate();
  // const { id } = useParams() as { id: string };
  const { variantid } = useParams() as { variantid: string };
  // const { code } = useParams() as { code: string };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      fixed: "left" as "left",
      render: (text: string, _: ProductVariantModiferGroupProps) => (
        <span onClick={() => console.log("hhhh")}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "Product Variant",
      dataIndex: "product_variant",
      key: "product_variant",
    },
    {
      title: "Modifier Group",
      dataIndex: "modifier_group",
      key: "modifier_group",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "In active"}</span>
      ),
    },
  ];

  const mapData = (response: any): ProductVariantModiferGroupProps[] =>
    response.objects.map((item: any) => ({
      key: item.id,
      id: item.id,
      position: item.position,
      product_variant: item.product_variant,
      is_active: item.is_active,
      modifier_group: item.modifier_group_name,
    }));
  return (
    <StoreTabs
      id={variantid}
      apiEndpoint="api/menu/v2/product-variant-modifier-groups"
      name="Product Variant MODIFIER GROUPS"
      columns={columns}
      dataMapper={mapData}
      // itemPath={itemPath}
      add={`/variants/modifier-groups/${variantid}/add/`}
    />
  );
};

export default VariantModiferGroup;
