import React from "react";
import { Button } from "antd";
import { DoubleLeftOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

// Define the type for the component props
interface BackButtonProps {
  to?: string;
  onClick?: () => void;
}

const BackButton: React.FC<BackButtonProps> = ({ onClick, to }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (to) {
      navigate(to);
    } else {
      navigate(-1);
    }
  };

  return (
    <Button
      type="primary"
      shape="round"
      icon={<DoubleLeftOutlined />}
      onClick={handleClick}
      className="backButton"
    >
      Back
    </Button>
  );
};

export default React.memo(BackButton);
