import React, { useCallback, useEffect, useState } from "react";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import { Table } from "antd";

import { MasterModifierProps } from "../../types";
import { useNavigate } from "react-router-dom";
import SearchBox from "../UI/SearchBox";

export interface Modifer {
  objects: MasterModifierProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const Modifiers: React.FC = () => {
  const [modifiers, setModifiers] = useState<Modifer | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const navigate = useNavigate();
  const [search, setSearch] = useState("");

  const getModifiers = async (page: number, search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(`api/menu/modifiers/`, {
        params: { page, search },
      });
      if (response.status === 200) {
        setModifiers(response.data);
      } else {
        console.error("Error fetching modifiers", response.status);
        setModifiers(null);
      }
    } catch (error) {
      console.error("Error fetching modifiers", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getModifiers(currentPage, search);
  }, [currentPage]);

  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    getModifiers(1, search);
  }, [search, currentPage, setCurrentPage]);

  const handleClear = useCallback(() => {
    setSearch("");
    setCurrentPage(1);
    getModifiers(1, "");
  }, [search, setCurrentPage, setSearch]);

  const columns = [
    // {
    //   title: "Code",
    //   dataIndex: "code",
    //   key: "code",
    //   width: "15%",
    //   render: (text: string, record: MasterModifierProps) => (
    //     <span onClick={() => navigate(`./${record.id}`)}>
    //       <Link>{text}</Link>
    //     </span>
    //   ),
    //   fixed: "left" as "left",
    // },
    {
      title: "Product ID",
      dataIndex: "id",
      key: "id",
      width: "20%",
      fixed: "left" as "left",
    },
    {
      title: "POS",
      dataIndex: "code",
      key: "code",
      width: "20%",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "20%",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "Inactive"}</span>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button
          className="typography"
          onClick={() => {
            navigate("./addmodifier");
          }}
        >
          {" "}
          + Add New{" "}
        </button>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">MODIFIERS</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            {/* <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="POS or Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getModifiers(currentPage, search)}>
                  Search
                </button>
              </div>
            </div> */}
            <div className="d-flex flex-wrap">
              <SearchBox
                value={search}
                onChange={setSearch}
                onSearch={() => handleSearch()}
                onClear={() => handleClear()}
                placeholder="Enter Code or Name"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <Table<MasterModifierProps>
          columns={columns}
          dataSource={modifiers?.objects || []}
          loading={loading}
          rowKey={(record) => String(record.id)}
          pagination={{
            current: currentPage,
            total: modifiers?.total_count || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
          }}
          scroll={{ x: 1100, y: 700 }}
        />
      </div>
    </div>
  );
};

export default Modifiers;
