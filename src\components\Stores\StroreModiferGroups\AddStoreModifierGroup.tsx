import { useEffect, useState } from "react";
import { Form, InputN<PERSON>ber, Button, Select, message } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import "../addStore/AddStore.css";

interface AddStoreModifierProps {
  section: string;
  is_active: boolean;
  max_selectable: number;
  store: number;
  modifier_group: number;
  position: number;
  name: string;
}
interface ListModifiersProps {
  id: number;
  name: string;
}
const AddStoreModifierGroup = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [listModiferGroup, setListModifierGroup] = useState<
    ListModifiersProps[]
  >([]);
//   const [loading, setLoading] = useState(false);
  const [modifierGroup, setModifierGroup] = useState<AddStoreModifierProps>({
    section: "",
    is_active: false,
    max_selectable: 0,
    store: Number(id),
    modifier_group: 0,
    position: 0,
    name: "",
  });
  const onValuesChange = (
    _: Partial<AddStoreModifierProps>,
    allValues: AddStoreModifierProps
  ) => {
    setModifierGroup({ ...allValues, store: Number(id) });
  };

  const onFinish = async () => {
    // setLoading(true);
    try {
      const response = await axiosInstance.post(
        `api/menu/store-modifier-groups/${id}/`,
        modifierGroup
      );
      if (response.status === 201) {
        message.success("Store Modifier group Created Successfully!");
        navigate(`/stores/${id}`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create Store Modifier group: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating Store Modifier group:", error);
        message.error("Failed to Create Store Modifier group.");
      }
    }
  };
  const fetchModifersList = async () => {
    try {
      const response = await axiosInstance.get(`api/menu/modifier-groups/`);
      if (response.status === 200) {
        setListModifierGroup(response.data.objects);
      } else {
        message.error("Error in fetching modifer groups");
        navigate("/stores");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to fetch: ${error.response.data.message || "Unknown error"}`
        );
      } else {
        console.error("Error fetch:", error);
        message.error("Failed Fetch.");
      }
    }
  };
  useEffect(() => {
    fetchModifersList();
  }, []);

  return (
    <div>
      <h3>Add Modifier Group</h3>
      <Form
        form={form}
        name="add_store"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={{ ...modifierGroup, modifier_group: "" }}
        className="add-store-form"
      >
        <Form.Item
          name="modifier_group"
          label="Modifier Group"
          rules={[{ required: true, message: "Please select Modifier Group" }]}
          className="form-item"
        >
          <Select className="input-field">
            {listModiferGroup?.map((modifier) => (
              <Select.Option key={modifier.id} value={modifier.id}>
                {modifier.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="section"
          label="Section"
          rules={[{ required: true, message: "Please enter store code" }]}
          className="form-item"
        >
          <Select className="input-field">
            <Select.Option value="customize">Customize</Select.Option>
            <Select.Option value="choose_side">Choose Side</Select.Option>
            <Select.Option value="choose_drink">Choose Drink</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="is_active"
          label="Active"
          rules={[{ required: true, message: "Please enter phone number" }]}
          className="form-item"
        >
          <Select className="input-field">
            <Select.Option value="true">Active</Select.Option>
            <Select.Option value="false">In Active</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="max_selectable"
          label="Max Selectable"
          rules={[{ required: true, message: "Please enter Max Selectable" }]}
          className="form-item"
        >
          <InputNumber min={0} max={100} className="input-field" />
        </Form.Item>
        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
          className="form-item"
        >
          <InputNumber min={0} max={100} className="input-field" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddStoreModifierGroup;