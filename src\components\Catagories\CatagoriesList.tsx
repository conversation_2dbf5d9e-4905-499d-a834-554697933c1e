import { useEffect, useRef, useState } from "react";
import { message, Space } from "antd";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import "../Products/Products.css";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import DataTable from "../UI/DataTable/DataTable";

import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import Link from "../UI/Link";
import showConfirmActionModal from "../UI/PopUpModal";
import Btn from "../UI/Btn";

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}
const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};
export interface Category {
  id: number;
  name: string;
  image_url: string;
  code: string;
  description: string;
  position: number;
}
interface CategoryWithKey extends Category {
  key: string;
}

const CategoriesList: React.FC = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteId, setDeleteId] = useState<number | null>(null);
  const [search, setSearch] = useState("");
  const [dragEnabled, setDragEnabled] = useState(false);
  const [editingId, _setEditingId] = useState<number | null>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);
  const [editingCategoryId, _setEditingCategoryId] = useState<number | null>(
    null
  );
  const positionInputRef = useRef<HTMLInputElement>(null);

  const getCategories = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(`/api/menu/categories/`, {
        params: { search },
      });
      if (response.status === 200) {
        console.log(response);
        const sorted = response.data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);

        setCategories(sorted);
      } else {
        message.error("Error fetching categories");
        setCategories([]);
      }
    } catch (error) {
      message.error("Error fetching categories");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCategories(search);
  }, []);
  useEffect(() => {
    console.log(categories);
  }, [dragEnabled]);

  // const showDeleteConfirm = (id: number) => {
  //   setDeleteId(id);
  //   setIsDeleteModalOpen(true);
  //   console.log(deleteId);
  // };

  // const handleDelete = async () => {
  //   if (!deleteId) return;
  //   try {
  //     const response = await axiosInstance.delete(`/api/menu/categories/${deleteId}/`);
  //     if (response.status === 204) {
  //       message.success("Category deleted successfully");
  //       getCategories(search);
  //     } else {
  //       message.error("Failed to delete category");
  //     }
  //   } catch (error) {
  //     message.error("Failed to delete category");
  //   } finally {
  //     setIsDeleteModalOpen(false);
  //     setDeleteId(null);
  //   }
  // };
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = categories.findIndex((c) => c.key === active.id);
    const newIndex = categories.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(categories, oldIndex, newIndex);
    setCategories(newOrder);
  };
  const handleSaveNewOrder = async () => {
    try {
      const updatedmodifiers = categories.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));
      const response = await axiosInstance.patch(
        `/api/menu/categories-position-update/`,
        {
          list: updatedmodifiers,
        }
      );
      if (response.status === 200) {
        message.success("Categories reordered successfully.");
        getCategories(search);
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };
  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );
  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);
  useEffect(() => {
    if (editingCategoryId !== null) {
      positionInputRef.current?.focus();
    }
  }, [editingCategoryId]);

  const handleEditConfirm = () => {
    showConfirmActionModal({
      isActive: true,
      onConfirm: handleSaveNewOrder,
      Text: "Position Update",
      entityName: "",
    });
  };

  const handleCancel = () => {
    setDragEnabled(false);
    message.info("Drag and drop is now disabled.");
  };

  const columns = [
    // {
    //   title: "ID",
    //   dataIndex: "id",
    //   key: "id",
    //   render: (text: string, record: Category) => (
    //     <span
    //       onClick={() =>
    //         navigate(`./${record.id}?name=${encodeURIComponent(record.name)}`)
    //       }
    //     >
    //       <Link>{text}</Link>
    //     </span>
    //   ),
    // },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      render: (text: string, record: Category) => (
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={`./${record.id}?name=${encodeURIComponent(record.name)}`}
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      render: (_: any, record: Category) => (
        <>
          {record.position}
          <Btn
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Btn>
          {dragEnabled && (
            <>
              <Space>
                <Btn icon={<CloseOutlined />} onClick={handleCancel} />

                <Btn
                  icon={<CheckOutlined />}
                  onClick={() => {
                    setDragEnabled(false);
                    handleEditConfirm();
                  }}
                />
              </Space>
            </>
          )}
        </>
      ),
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: Category) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <Link to={`./addcategory`}>
          <button
            className="typography"
            onClick={() => navigate("./addcategory")}
          >
            + Add New
          </button>
        </Link>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Categories </div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search by Nme"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getCategories(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="">
        <DndContext
          sensors={sensors}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={categories.map((item) => item.key)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable
              loading={loading}
              columns={columns}
              dataSource={categories}
              components={{
                body: {
                  row: (props: RowProps) => (
                    <DragRow {...props} dragEnabled={dragEnabled} />
                  ),
                },
              }}
              rowKey="key"
            />
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
};

export default CategoriesList;
