import React, { useCallback, useEffect, useState } from "react";
import { Spin, Button, Upload, notification, message, Modal } from "antd";
import {
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  UploadOutlined,
} from "@ant-design/icons"; // <-- Imported icons
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";

import "../../Products/Products.css";
import { RcFile } from "antd/es/upload";
import axios from "axios";

interface Category {
  id: number;
  name: string;
  code: string;
  description: string;
  image_url: string;
  crown_category: boolean;
  recommendation_category: boolean;
  position: number;
}

const CategoryDetails: React.FC = () => {
  // const [form] = Form.useForm();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [categoryDetails, setCategoryDetails] = useState<Category | null>(null);
  const [uploading, setUploading] = useState(false);

  // Fetch category details from the API
  const fetchCategoryDetails = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `/api/menu/v2/categories/${id}/`
      );
      if (response.status === 200) {
        setCategoryDetails(response.data);
      } else {
        setCategoryDetails(null);
      }
    } catch (error) {
      console.error("Error fetching category data", error);
      setCategoryDetails(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchCategoryDetails();
    }
  }, [id]);

  const handleEdit = () => {
    navigate(`/categories/edit/${id}`);
  };

  const handleSwitchChange = async (
    key: "crown_category" | "recommendation_category",
    checked: boolean
  ) => {
    if (!categoryDetails) return;

    const oppositeKey =
      key === "crown_category" ? "recommendation_category" : "crown_category";

    try {
      await axiosInstance.put(`/api/menu/v2/categories/${id}/`, {
        [key]: checked,
        [oppositeKey]: checked ? false : categoryDetails[oppositeKey],
      });
      setCategoryDetails((prev) =>
        prev
          ? {
              ...prev,
              [key]: checked,
              [oppositeKey]: checked ? false : prev[oppositeKey],
            }
          : prev
      );
      message.success(`Updated ${key.replace("_", " ")}`);
    } catch (error) {
      notification.error({
        message: "Update Failed",
        description: `Could not update ${key.replace("_", " ")}.`,
      });
    }
  };

  // ✅ Fix handleStatusChange type and logic
  const handleStatusChange = useCallback(
    (key: "crown_category" | "recommendation_category", isActive: boolean) => {
      Modal.confirm({
        title: isActive ? "Activate Category" : "Deactivate Category",
        content: isActive
          ? `Are you sure you want to activate ${key.replace("_", " ")}?`
          : `Are you sure you want to deactivate ${key.replace("_", " ")}?`,
        okText: "Yes",
        cancelText: "No",
        className: "custom-modal",
        okButtonProps: { className: "custom-modal-ok-button" },
        cancelButtonProps: { className: "custom-modal-cancel-button" },
        onOk: async () => {
          await handleSwitchChange(key, isActive);
        },
      });
    },
    [categoryDetails, id]
  );

  const formatValue = (value: any): string => {
    if (value === undefined || value === null || value === "") return "-";

    if (value === "image_url") return "-";

    return value;
  };

  // Render category details in a card-like layout
  const renderDetailsInCard = () => {
    if (!categoryDetails) {
      return <div>No category data available.</div>;
    }

    const fields = [
      { key: "id", label: "ID" },
      { key: "name", label: "Name" },
      { key: "code", label: "Code" },
      { key: "description", label: "Description" },
      { key: "position", label: "Position" },
      //
      { key: "crown_category", label: "Crown Category" },
      { key: "recommendation_category", label: "Recommendation Category" },

      // { key: "image_url", label: "Image URL" },
    ];

    return fields.map((field) => (
      <div key={field.key} className="order-details-value">
        <div className="order-details-label">{field.label}</div>
        <span className="order-details-value-colon">:</span>
        <span className="order-details-value-value">
          {field.key === "crown_category" ||
          field.key === "recommendation_category" ? (
            // <Switch
            //   checked={!!categoryDetails[field.key as "crown_category" | "recommendation_category"]}
            //   onChange={(checked) =>
            //     handleSwitchChange(field.key as "crown_category" | "recommendation_category", checked)
            //   }
            // />
            <div className="d-flex">
              <div
                className={`switch-button ${
                  categoryDetails[field.key as keyof Category] ? "checked" : ""
                }`}
                onClick={() =>
                  handleStatusChange(
                    field.key as "crown_category" | "recommendation_category",
                    !categoryDetails[field.key as keyof Category]
                  )
                }
              >
                <span className="switch-label">
                  {categoryDetails[field.key as keyof Category] ? (
                    <CheckOutlined />
                  ) : (
                    <CloseOutlined />
                  )}
                </span>
                <div className="switch-handle"></div>
              </div>
            </div>
          ) : (
            formatValue(categoryDetails[field.key as keyof Category])
          )}
        </span>
      </div>
    ));
  };

  const getPresignedUrl = async (file: RcFile) => {
    try {
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "image_url",
        }
      );

      const { url, fields } = response.data?.url || {};
      if (!url || !fields) return null;

      return { url, fields, key: fields.key };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    fieldType: "image_url"
  ) => {
    setUploading(true);
    try {
      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });

      await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (file: RcFile, fieldType: "image_url") => {
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) return;
    await uploadToS3(uploadData, file, fieldType);
  };

  const updateProductImage = async (
    imageUrl: string,
    fieldType: "image_url"
  ) => {
    try {
      await axiosInstance.put(`api/menu/v2/categories/${id}/`, {
        [fieldType]: imageUrl,
      });
      fetchCategoryDetails();
    } catch (error) {
      notification.error({
        message: "Update Failed",
        description: "Failed to update product image.",
      });
    }
  };

  const getUploadProps = (fieldType: "image_url") => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false;
    },
    showUploadList: false,
  });

  return (
    <div>
      {loading ? (
        <div className="d-flex justify-content-center align-items-center">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div className="d-lg-flex align-items-center justify-content-end">
            <Button
              type="primary"
              className="backButton"
              icon={<EditOutlined />}
              onClick={handleEdit}
            >
              Edit
            </Button>
          </div>
          {renderDetailsInCard()}
          <div>
            <span className="font-family-Poppins font-size-16 font-weight-bold mb-4 p-1">
              Category Image
            </span>
          </div>
          <div>
            {categoryDetails?.image_url ? (
              <div className="d-inline-block position-relative mt-3">
                <img
                  src={categoryDetails?.image_url}
                  alt="category-image"
                  className="w-32 rounded-md shadow"
                />

                <Upload {...getUploadProps("image_url")}>
                  <Button
                    icon={<EditOutlined />}
                    loading={uploading}
                    className="edit-upload-button"
                  />
                </Upload>
              </div>
            ) : (
              <div className="mt-2">
                <Upload {...getUploadProps("image_url")}>
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploading}
                    className="mt-2"
                  >
                    Upload Large Image
                  </Button>
                </Upload>
              </div>
            )}
          </div>

          {/* Thumbnail Image
          <div>
            <strong>Thumbnail:</strong>
            {categoryDetails?.image_url ? (
              <div className="relative w-fit mt-2">
                <img
                  src={String(categoryDetails.image_url)}
                  alt="Thumbnail"
                  className="w-20 rounded shadow"
                />
                <Upload {...getUploadProps("image_url")}>
                  <Button
                    icon={<EditOutlined />}
                    loading={uploading}
                    className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-gray-100"
                  />
                </Upload>
              </div>
            ) : (
              <Upload {...getUploadProps("image_url")}>
                <Button
                  icon={<UploadOutlined />}
                  loading={uploading}
                  className="mt-2"
                >
                  Upload Thumbnail
                </Button>
              </Upload>
            )}
          </div> */}
        </>
      )}
      <br />
      <br />
    </div>
  );
};

export default CategoryDetails;
