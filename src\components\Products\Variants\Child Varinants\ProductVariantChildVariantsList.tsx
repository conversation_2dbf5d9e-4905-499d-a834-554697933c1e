import { ProductVariantChildVariantProps } from "../../../../types/Products";
import { InputNumber, message, Select } from "antd";
import { useParams } from "react-router-dom";
import StoreTabs from "../../../../reusableComponents/store/StoreTabs";
import { useState } from "react";
import { axiosInstance } from "../../../../apiCalls";
import StatusToggle from "../../../UI/ToggleStatus";
import showConfirmActionModal from "../../../UI/PopUpModal";
import axios from "axios";
import Btn from "../../../UI/Btn";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";

const ProductVariantChildVariantsList = () => {
  const { variantid } = useParams() as { variantid: string };

  const [editingRowPosition, setEditingRowPosition] = useState<number | null>(
    null
  );
  const [editingRowSection, setEditingRowSection] = useState<number | null>(
    null
  );

  const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  const handleStatusChange = (
    id: number,
    parentId: number,
    newValue: boolean | number | string,
    field: "is_active" | "position" | "section"
  ) => {
    const confirmUpdate = async () => {
      try {
        const payload: Record<string, any> = { [field]: newValue };
        const response = await axiosInstance.patch(
          `api/menu/v2/child-variants/${parentId}/${id}/`,
          payload
        );

        if (response.status === 200) {
          message.success(`Child Variant ${field} updated successfully`);
          setRefreshTrigger((p) => p + 1);
          setEditingRowPosition(null);
          setEditingRowSection(null);
        } else {
          message.error(`Failed to update ${field}.`);
        }
      } catch (err: unknown) {
        if (axios.isAxiosError(err)) {
          message.error(
            err.response?.data?.message ||
              `An error occurred while updating ${field}.`
          );
        } else {
          message.error("An unexpected error occurred.");
        }
      }
    };

    if (field === "is_active") {
      showConfirmActionModal({
        isActive: newValue as boolean,
        onConfirm: confirmUpdate,
        entityName: "Product Child Variant",
      });
    } else {
      confirmUpdate();
    }
  };

  const columns = [
    {
      title: "POS Number",
      dataIndex: "child_pos",
      key: "child_pos",
      width: "10%",
      fixed: "left" as "left",
    },
    {
      title: "Child Code",
      dataIndex: "child_code",
      key: "child_code",
      width: "10%",
    },
    {
      title: "Child",
      dataIndex: "child",
      key: "child",
      width: "10%",
    },
    {
      title: "Name",
      dataIndex: "child_name",
      key: "child_name",
      width: "20%",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "20%",
      render: (text: number, record: ProductVariantChildVariantProps) =>
        editingRowPosition === record.id ? (
          <span style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <InputNumber
              value={editingPosition ?? record.position}
              onChange={(val) => setEditingPosition(val ?? 0)}
              min={0}
            />
            <Btn
              onClick={() =>
                handleStatusChange(
                  record.id,
                  record.parent,
                  editingPosition ?? record.position,
                  "position"
                )
              }
            >
              <CheckOutlined style={{ color: "green" }} />
            </Btn>

            <Btn onClick={() => setEditingRowPosition(null)}>
              <CloseOutlined style={{ color: "red" }} />
            </Btn>
          </span>
        ) : (
          <span>
            {text}
            <Btn
              type="link"
              onClick={() => {
                setEditingRowPosition(record.id);
                setEditingPosition(record.position);
                setEditingSection(null);
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Btn>
          </span>
        ),
    },
    {
      title: "Section",
      dataIndex: "section",
      key: "section",
      width: "25%",
      render: (text: string, record: ProductVariantChildVariantProps) =>
        editingRowSection === record.id ? (
          <span
            style={{
              display: "flex",
              padding: "0px",
              alignItems: "center",
              gap: "10px",
            }}
          >
            <Select
              placeholder="Select section"
              className="input-field"
              value={editingSection ?? record.section}
              onChange={(val) => setEditingSection(val)}
              style={{ minWidth: 120 }}
            >
              <Select.Option value="customize">Customize</Select.Option>
              <Select.Option value="choose_side">Choose Side</Select.Option>
              <Select.Option value="choose_drink">Choose Drink</Select.Option>
            </Select>
            <Btn
              onClick={() =>
                handleStatusChange(
                  record.id,
                  record.parent,
                  editingSection ?? record.section,
                  "section"
                )
              }
            >
              <CheckOutlined style={{ color: "green" }} />
            </Btn>
            <Btn onClick={() => setEditingRowSection(null)}>
              <CloseOutlined style={{ color: "red" }} />
            </Btn>
          </span>
        ) : (
          <span>
            {text}
            <Btn
              type="link"
              onClick={() => {
                setEditingRowSection(record.id);
                setEditingSection(record.section);
                setEditingPosition(null);
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Btn>
          </span>
        ),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (is_active: boolean, record: ProductVariantChildVariantProps) => (
        <StatusToggle
          isActive={is_active}
          id={record.id}
          onToggle={() =>
            handleStatusChange(
              record.id,
              record.parent,
              !record.is_active,
              "is_active"
            )
          }
        />
      ),
    },
  ];

  const mapData = (response: any): ProductVariantChildVariantProps[] => {
    if (!Array.isArray(response)) {
      console.error("Invalid API response format:", response);
      return [];
    }
    return response.map((item: any) => ({
      key: item.id,
      id: item.id,
      child_code: item.child_code,
      child_pos: item.child_pos,
      section: item.section,
      parent: item.parent,
      child: item.child,
      position: item.position,
      child_name: item.child_name,
      is_active: item.is_active,
    }));
  };

  return (
    <>
      <StoreTabs
        id={variantid}
        apiEndpoint="api/menu/v2/child-variants"
        name="Child Variants"
        columns={columns}
        dataMapper={mapData}
        add={`/products/variants/${variantid}/childvariant/add`}
        refreshTrigger={refreshTrigger}
      />
    </>
  );
};

export default ProductVariantChildVariantsList;
