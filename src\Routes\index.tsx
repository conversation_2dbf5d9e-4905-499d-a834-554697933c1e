import { Suspense } from "react";
import { BrowserRouter, Navigate, Routes, Route } from "react-router-dom";
import Cookies from "js-cookie";
import "bootstrap/dist/css/bootstrap.min.css";
import { ProductProvider } from "../context/ProductContext";
import LoginPage from "../pages/LoginPage";
import PrivateRoute from "../PrivateRoute/PrivateRoute";
import LayoutDesign from "../components/Layout/LayoutDesign";
import { routes } from "./Route";
import { authRoutes } from "./AuthRoute";

const RouterData: React.FC = () => {
  const token = Cookies.get("token");
  return (
    <ProductProvider>
      <BrowserRouter basename="/">
        {/* <DynamicBreadcrumb /> */}
        <Suspense
          fallback={
            <div
              className="d-flex justify-content-center align-items-center"
              style={{ height: "100vh" }}
            ></div>
          }
        >
          <Routes>
            {/* {token ? (
              <>
                <Route path={`/`} element={<Navigate to={`/products`} />} />
              </>
            ) : (
              ""
            )} */}
            <Route
              path="/"
              element={token ? <Navigate to="/products" /> : <LoginPage />}
            />
            {/* Public Route */}
            {/* <Route path="/" element={<LoginPage />} /> */}
            {/* Protected Routes */}

            {/* <Route path="/" element={<LoginPage />} /> */}
            {/* <Route element={<LayoutDesign />}> */}
            {/* Protected Routes with LayoutDesign inside PrivateRoute */}
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <LayoutDesign />
                </PrivateRoute>
              }
            >
              {routes.map(({ path, element }, i) => (
                <Route key={i} path={path} element={element} />
              ))}
            </Route>

            {authRoutes.map(({ path, element }, i) => (
              <Route key={i} path={path} element={element} />
            ))}
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ProductProvider>
  );
};

export default RouterData;
