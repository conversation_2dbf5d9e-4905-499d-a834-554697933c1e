import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { setTokens, getAccessToken, getRefreshToken } from "../../utils/tokenUtils";

const AuthRedirect = () => {
  const navigate = useNavigate();

  console.log("AuthRedirect");

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const token = params.get("token"); // Look for 'token' parameter in URL
    const refreshToken = params.get("refreshToken"); // Look for 'refreshToken' parameter in URL

    console.log("Token from URL: ", token);
    console.log("Refresh Token from URL: ", refreshToken);

    if (token) {
      console.log("Setting tokens using utility function...");

      // Use the utility function to set tokens in both cookies and localStorage
      setTokens(token, refreshToken || undefined);

      // Verify tokens were set
      console.log("Tokens after setting:");
      console.log("Access Token:", getAccessToken());
      console.log("Refresh Token:", getRefreshToken());

      // Clean the URL (good practice)
      window.history.replaceState({}, document.title, "/orderlist");

      // Navigate to CMS dashboard
      navigate("/orderlist", { replace: true });
    } else {
      console.error("No token found in URL parameters");
      navigate("/");
    }
  }, [navigate]);

  return null;
};

export default AuthRedirect;
