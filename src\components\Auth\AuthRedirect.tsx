import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { ACCESS_TOKEN } from "../../Constant";

const AuthRedirect = () => {
  const navigate = useNavigate();

  console.log("AuthRedirect");

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const token = params.get(ACCESS_TOKEN);

    console.log("Token from URL: ", token);

    if (token) {
      // Store token in CMS cookies
      Cookies.set(ACCESS_TOKEN, token, { sameSite: "Strict", secure: true });

      // Clean the URL (good practice)
      window.history.replaceState({}, document.title, "/orderlist");

      // Navigate to CMS dashboard
      navigate("/orderlist", { replace: true });
    } else {
      navigate("/");
    }
  }, [navigate]);

  return null;
};

export default AuthRedirect;
