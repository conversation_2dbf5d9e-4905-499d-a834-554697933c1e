import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { ACCESS_TOKEN, REFRESH_TOKEN } from "../../Constant";

const AuthRedirect = () => {
  const navigate = useNavigate();

  console.log("AuthRedirect");

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const token = params.get("token"); // Look for 'token' parameter in URL
    const refreshToken = params.get("refreshToken"); // Look for 'refreshToken' parameter in URL

    console.log("Token from URL: ", token);
    console.log("Refresh Token from URL: ", refreshToken);

    if (token) {
      // Store token in cookies using the ACCESS_TOKEN constant as the cookie name
      // Match the same cookie settings as login page for consistency
      Cookies.set(ACCESS_TOKEN, token, {
        sameSite: "Strict",
        secure: true,
        expires: 0.125 // 3 hours, same as login page
      });

      // Store refresh token if provided
      if (refreshToken) {
        Cookies.set(REFRESH_TOKEN, refreshToken, {
          sameSite: "Strict",
          secure: true,
          expires: 0.125 // 3 hours, same as login page
        });
      }

      // Clean the URL (good practice)
      window.history.replaceState({}, document.title, "/orderlist");

      // Navigate to CMS dashboard
      navigate("/orderlist", { replace: true });
    } else {
      navigate("/");
    }
  }, [navigate]);

  return null;
};

export default AuthRedirect;
