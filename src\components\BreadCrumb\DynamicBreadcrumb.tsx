import React from "react";
import { Breadcrumb } from "antd";
import { Link, useLocation } from "react-router-dom";

const DynamicBreadcrumb: React.FC = () => {
  const location = useLocation();

  // Extract pathname and remove query params
  const pathSegments = location.pathname.split("/").filter((segment) => segment);

  // Generate breadcrumb items
  const breadcrumbItems = [
    { title: <Link to="/">Home</Link> }, // Home link
    ...pathSegments.map((segment, index) => {
      const url = `/${pathSegments.slice(0, index + 1).join("/")}`;
      const isLast = index === pathSegments.length - 1;

      return {
        title: isLast ? decodeURIComponent(segment.replace(/-/g, " ")) : <Link to={url}>{decodeURIComponent(segment.replace(/-/g, " "))}</Link>,
      };
    }),
  ];

  return <Breadcrumb items={breadcrumbItems} />;
};

export default DynamicBreadcrumb;
