import { AutoComplete, Input } from "antd";
import { axiosInstance } from "../../apiCalls";
import { useEffect, useState } from "react";
import { AutoCompleteOptions, getStoresParams, StoreDropdownProps, StoreObject } from "./types";


export default function StoresDropdown({
    onChange,
    placeholder,
    value,
    ...props
}: StoreDropdownProps) {
    const [options, setOptions] = useState<AutoCompleteOptions>({
        page: 1,
        pageSize: 10,
        data: [],
        totalPages: 1,
    });
    const [isLoading, setisLoading] = useState<boolean>(false);
    const getStores = async (params: getStoresParams, shouldAppend?: boolean) => {
        setisLoading(true);
        if (!shouldAppend) {
            setOptions({
                page: 1,
                pageSize: 10,
                data: [],
                totalPages: 1
            });
        }
        try {
            const resData = (await axiosInstance.get('api/stores', { params })).data;
            setOptions((prevOptions) => {
                let data = resData?.objects?.map((store: StoreObject) => ({ key:store?.id,label: store?.name, value: store?.name, ...store }));
                if (shouldAppend) {
                    data = [...prevOptions?.data, ...data];
                }
                return {
                    page: resData?.current_page,
                    data: data,
                    pageSize: resData?.page_size,
                    totalPages: resData?.total_pages
                }
            });
        } catch (err) {
            console.log(err);
        } finally {
            setisLoading(false);
        }
    }
    useEffect(() => {
        getStores({ page: 1 }, false);
    }, []);
    const onScrollReachedEnd = () => {
        if (options?.page + 1 <= options?.totalPages) {
            getStores({
                page: options?.page + 1,
            }, true);
        }
    }
    const onPopupScroll = (ev: any) => {
        const target = ev.target;
        if (target.scrollTop + target.offsetHeight >= target.scrollHeight) {
            onScrollReachedEnd();
        }
    }
    const onInputChange = (value: string) => {
        getStores({
            search: value,
            page: 1,
        }, false);
        onChange(null);
    }
    const onOptionSelect = (_: string, option: StoreObject) => {
        onChange(option);
    }
    return (
        <AutoComplete
            options={options?.data}
            onPopupScroll={onPopupScroll}
            onSelect={onOptionSelect}
            onChange={onInputChange}
            dropdownRender={(menu) => {
                return <>
                    {menu}
                    {isLoading && <p style={{ padding: '5px 12px', marginBottom: 0 }}>Loading ...</p>}
                </>
            }}
            {...props}
        >
            <Input placeholder={placeholder} />
        </AutoComplete>
    );
}