import React, { useState } from "react";
// import { Form, Input, InputNumber, Button, Select, message } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate } from "react-router-dom";
import "./AddStore.css";
import StoreForm from "../StoreForm/StoreForm";
import { Form, message } from "antd";

interface AddStoreProps {
  name: string;
  code: string;
  phone: string;
  postal_code: string;
  address: string;
  latitude: number;
  longitude: number;
  tax_percentage: number;
  third_party_id: string;
  coverage_type: string;
  business: number;
  ato_id:string;
}

const AddStore: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const [storeData, setStoreData] = useState<AddStoreProps>({
    name: "",
    code: "",
    phone: "",
    postal_code: "",
    address: "",
    latitude: 0.0,
    longitude: 0.0,
    tax_percentage: 10,
    third_party_id: "",
    coverage_type: "Radius",
    business: 1,
    ato_id:"",
  });

  // const onValuesChange = (
  //   _: Partial<AddStoreProps>,
  //   allValues: AddStoreProps
  // ) => {
  //   setStoreData(allValues);
  // };

  const onFinish = async () => {
    try {
      const response = await axiosInstance.post(`api/stores/`, storeData);
      if (response.status === 201) {
        message.success("Store Created Successfully!");
        navigate("/stores");
      }
    } catch (error: any) {
      if (error.response) {
        const apiError = error.response.data;
  
        const generalError = apiError.message || apiError.error;
        if (generalError) {
          message.error(`Failed to create store: A store with this third party id exists `);
        }
  
        if (apiError.errors) {
          const fieldErrors = (Object.entries(apiError.errors) as [string, string[] | string][]).map(
            ([field, messages]) => ({
              name: field,
              errors: Array.isArray(messages) ? messages : [messages],
            })
          );
  
          form.setFields(fieldErrors);
        }
      } else {
        console.error("Unexpected error:", error);
        message.error("Failed to create store: A store with this third party id exists.");
      }
    }
  };
  
  
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Add Store</h2>
      <StoreForm
        form={form}
        initialValues={storeData}
        onValuesChange={(_, allValues) => setStoreData((prev) => ({ ...prev, ...allValues }))}
        onFinish={onFinish}
      />
    </div>
  );
};

export default AddStore;
