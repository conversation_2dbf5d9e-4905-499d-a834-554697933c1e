import { useEffect, useState } from "react";
import { ProductVariantProps } from "../../../types/Products";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  Card,
  Spin,
  notification,
  Button,
  Upload,
  Form,
  Select,
  Modal,
} from "antd";
import VariantModiferGroup from "./Modifier Groups/VariantModiferGroup";
import ProductVariantChildVariantsList from "./Child Varinants/ProductVariantChildVariantsList";
import type { RcFile } from "antd/es/upload";
import { EditOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import ListingProductVariantStores from "./Stores/ListingProductVariantStores";
import axios from "axios";
import BackButton from "../../UI/BackButton";
import { UNAVAILABILITY_TAGS_CHOICES } from "../../ProductVariants/MasterLevelVariantEdit";
import { ALERT_MESSAGE } from "./Constant/Constant";
import Btn from "../../UI/Btn";
import AppTag from "../../UI/Tag/index";
import BulkPriceSync from "./BulkPriceSync/BulkPriceSync";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "ModifierGroups", tab: " Modifier Groups" },
  { key: "Child_Variants", tab: "Child Variants" },
  { key: "Stores", tab: "Stores" },
];

const ProductVariantDetails = () => {
  const [variantDetails, setVariantDetails] =
    useState<ProductVariantProps | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTabKey1 = searchParams.get("tab") || "Details";
  const { variantid } = useParams();
  // const categoryId = searchParams.get("categoryId");
  // const categoryName = searchParams.get("categoryName");
  const navigate = useNavigate();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTags, setEditingTags] = useState<string[]>([]);

  const [isPriceModalVisible, setIsPriceModalVisible] = useState(false);

  const fetchVariant = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `api/menu/v2/product-variants/${variantid}/${variantid}/`
      );
      if (response.status === 200) {
        setVariantDetails(response.data);
      } else {
        setVariantDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVariant();
  }, []);

  const onTabChange = (key: string) => {
    setSearchParams({ tab: key });
  };

  const getPresignedUrl = async (file: RcFile) => {
    try {
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      const { url, fields } = response.data?.url || {};
      if (!url || !fields) return null;

      return { url, fields, key: fields.key };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    setUploading(true);
    try {
      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });

      await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) return;
    await uploadToS3(uploadData, file, fieldType);
  };

  const updateProductImage = async (
    imageUrl: string,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    try {
      await axiosInstance.patch(
        `api/menu/v2/product-variants/${variantid}/${variantid}/`,
        {
          [fieldType]: imageUrl,
        }
      );
      fetchVariant();
    } catch (error) {
      notification.error({
        message: "Update Failed",
        description: "Failed to update product image.",
      });
    }
  };

  const getUploadProps = (
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false;
    },
    showUploadList: false,
  });

  const uploadButton = (
    <div style={{ border: 0, background: "none", textAlign: "center" }}>
      {uploading ? (
        <LoadingOutlined style={{ fontSize: 24 }} />
      ) : (
        <PlusOutlined style={{ fontSize: 24 }} />
      )}
      <div style={{ marginTop: 8 }}>
        {uploading ? "Uploading..." : "Upload"}
      </div>
    </div>
  );

  // const handleStatusToggle = async () => {
  //   const currentStatus = variantDetails?.is_available;
  //   Modal.confirm({
  //     title: currentStatus ? "Deactivate Product" : "Activate Product",
  //     content: `Are you sure you want to ${
  //       currentStatus ? "deactivate" : "activate"
  //     } this product variant?`,
  //     okText: "Yes",
  //     cancelText: "No",
  //     className: "custom-modal",
  //     okButtonProps: { className: "custom-modal-ok-button" },
  //     cancelButtonProps: { className: "custom-modal-cancel-button" },
  //     onOk: async () => {
  //       try {
  //         const response = await axiosInstance.patch(
  //           `api/menu/update-product-avaiablity/${variantid}/`,
  //           { is_available: !currentStatus }
  //         );
  //         notification.success({
  //           message: `Product ${
  //             !currentStatus ? "activated" : "deactivated"
  //           } successfully`,
  //           description: Array.isArray(response.data?.message)
  //             ? response.data.message.join(", ")
  //             : response.data?.message || "", // fallback to string or empty
  //         });
  //         fetchVariant(); // refresh the UI
  //       } catch (error) {
  //         notification.error({
  //           message: "Update Failed",
  //           description: "Failed to update availability status.",
  //         });
  //       }
  //     },
  //   });
  // };

  const handleClosePriceModal = () => {
    setIsPriceModalVisible(false);
  };

  const contentList: Record<string, React.ReactNode> = {
    Details: variantDetails ? (
      <div className="space-y-4">
        {[
          // { label: "Id", key: "id" },
          { label: "Code", key: "code" },
          { label: "Name", key: "name" },
          { label: "SKU", key: "sku" },
          { label: "Display Name", key: "display_name" },
          { label: "Description", key: "description" },
          { label: "Price", key: "price" },
          { label: "discounted Price", key: "discounted_price" },
          // { label: "Availability Status", key: "is_available" },
          { label: "Product Type", key: "product_type" },
          { label: "Unavailability Tags", key: "unavailability_tags" },
          { label: "Tags", key: "tags" },
          { label: "Base Product", key: "base_product_name" },
          { label: "Is Child Variant", key: "is_child_variant" },
          { label: "Default Pop Variant", key: "default_pop_variant_name" },
          { label: "Crown Product", key: "is_crown_product" },
          { label: "Loyalty Offer Code", key: "loyalty_offer_code" },
          { label: "Crown Points", key: "crown_points" },
        ].map((field) => {
          const value = variantDetails[field.key as keyof ProductVariantProps];
          let formattedValue;

          if (value === undefined || value === null || value === "") {
            formattedValue = "-";
          } else if (typeof value === "boolean") {
            formattedValue = value ? "Yes" : "No";
          } else if (typeof value === "number") {
            formattedValue = value.toFixed(1);
          } else if (Array.isArray(value)) {
            formattedValue = value.length ? value.join(", ") : "-";
          } else {
            formattedValue = value;
          }

          // Skip loyalty fields if not a crown product
          if (
            !variantDetails.is_crown_product &&
            (field.key === "loyalty_offer_code" || field.key === "crown_points")
          ) {
            return null;
          }

          return (
            <div key={field.key} className="order-details-value">
              <div className="order-details-label">{field.label}</div>
              <span className="order-details-value-colon">:</span>
              <span className="order-details-value-value">
                <div className="d-flex flex-wrap gap-0 items-center">
                  {/* {formattedValue} */}
                  <span className="order-details-value-value">
                    <div className="d-flex flex-wrap gap-0 items-center">
                      {field.key === "unavailability_tags" &&
                      Array.isArray(value) ? (
                        value.map((tag) => (
                          <AppTag
                            key={tag}
                            color="success"
                            className="fs-6 family-font-Poppins rounded-pill"
                          >
                            {tag}
                          </AppTag>
                        ))
                      ) : field.key === "price" ||
                        field.key === "discounted_price" ? (
                        <AppTag
                          color="success"
                          className="fs-6 family-font-Poppins rounded-pill"
                        >
                          {formattedValue}
                        </AppTag>
                      ) : (
                        formattedValue
                      )}

                      {field.key === "unavailability_tags" && (
                        <Button
                          type="link"
                          className="edit-btn"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingTags(Array.isArray(value) ? value : []);
                            setIsModalVisible(true);
                          }}
                        />
                      )}

                      {(field.key === "price" ||
                        field.key === "discounted_price") && (
                        <Btn
                          type="link"
                          className="edit-btn"
                          icon={<EditOutlined />}
                          onClick={() => setIsPriceModalVisible(true)}
                        />
                      )}
                    </div>
                  </span>
                </div>
              </span>
            </div>
          );
        })}

        {/* Large Image */}
        <div>
          <strong>Large Image:</strong>
          {variantDetails.image_large_url ? (
            <div className="relative w-fit mt-2">
              <img
                src={String(variantDetails.image_large_url)}
                alt="Large"
                className="w-32 rounded-md shadow"
              />
              <Upload {...getUploadProps("image_large_url")}>
                <Button
                  icon={<EditOutlined />}
                  loading={uploading}
                  className="edit-upload-button"
                  // className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-gray-100"
                />
              </Upload>
            </div>
          ) : (
            <Upload
              listType="picture-card"
              {...getUploadProps("image_large_url")}
            >
              {uploadButton}
            </Upload>
          )}
        </div>

        {/* Thumbnail Image */}
        <div>
          <strong>Thumbnail:</strong>
          {variantDetails.image_thumbnail_url ? (
            <div className="relative w-fit mt-2">
              <img
                src={String(variantDetails.image_thumbnail_url)}
                alt="Thumbnail"
                className="w-20 rounded shadow"
              />
              <Upload {...getUploadProps("image_thumbnail_url")}>
                <Button
                  icon={<EditOutlined />}
                  loading={uploading}
                  className="edit-upload-button"
                  //className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-gray-100"
                />
              </Upload>
            </div>
          ) : (
            <Upload
              listType="picture-card"
              {...getUploadProps("image_thumbnail_url")}
            >
              {uploadButton}
            </Upload>
          )}
        </div>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    ModifierGroups: <VariantModiferGroup />,
    Stores: <ListingProductVariantStores />,
    Child_Variants: <ProductVariantChildVariantsList />,
  };

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div>
            <BackButton
            // to={
            //   categoryId && categoryName
            //     ? `/categories/${categoryId}/?tab=ProductVariants&name=${categoryName}`
            //     : `/variants`
            // }
            />
          </div>
          <Card
            style={{ width: "100%" }}
            title={
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">
                  {variantDetails?.name}
                </h3>
                <div style={{ backgroundColor: "#ff8732" }}>
                  <Button
                    icon={<EditOutlined />}
                    type="text"
                    className="hover:text-blue-500 "
                    onClick={() => {
                      // Add edit handler logic here
                      navigate(
                        `/variant/${variantDetails?.id}/EditProductVariant?name=${variantDetails?.name}`
                      );
                    }}
                  />
                </div>
              </div>
            }
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTabChange}
          >
            {contentList[activeTabKey1]}
          </Card>

          <Modal
            title={
              <div className="d-flex justify-content-between align-items-center px-3 pt-2">
                <div className="alert alert-warning rounded-pill fw-semibold py-2 px-3 mb-0 w-100 text-center">
                  {ALERT_MESSAGE}
                </div>
              </div>
            }
            maskClosable={false}
            open={isModalVisible}
            onCancel={() => setIsModalVisible(false)}
            onOk={async () => {
              try {
                await axiosInstance.patch(
                  `api/menu/v2/product-variants/${variantid}/${variantid}/`,
                  { unavailability_tags: editingTags }
                );
                notification.success({ message: "Updated successfully" });
                fetchVariant();
                setIsModalVisible(false);
              } catch (error) {
                notification.error({
                  message: "Update Failed",
                  description: "Could not update unavailability tags.",
                });
              }
            }}
            okText="Update"
            cancelText="Cancel"
            okButtonProps={{
              className: "px-4 custom-modal-ok-button",
            }}
            cancelButtonProps={{
              className: "y px-4 custom-modal-cancel-button",
            }}
            className=""
          >
            <div className="p-3 border-1 border-gray-200 rounded-md">
              <Form layout="vertical">
                <Form.Item
                  label={
                    <span className="fw-bold text-dark">
                      Unavailability Tags
                    </span>
                  }
                >
                  <Select
                    mode="multiple"
                    value={editingTags}
                    onChange={(tags) => setEditingTags(tags)}
                    placeholder="Select unavailability tags"
                  >
                    {UNAVAILABILITY_TAGS_CHOICES.map((type) => (
                      <Select.Option key={type.value} value={type.value}>
                        {type.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Form>
            </div>
          </Modal>
          {variantDetails?.id !== undefined && (
            <BulkPriceSync
              visible={isPriceModalVisible}
              onClose={handleClosePriceModal}
              productId={variantDetails.id}
              onSuccess={fetchVariant}
            />
          )}
        </>
      )}
    </>
  );
};

export default ProductVariantDetails;
