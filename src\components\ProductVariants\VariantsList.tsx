import { useCallback, useEffect, useMemo, useState } from "react";
import { Modal, message } from "antd";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import "../Products/Products.css";
import SearchBox from "../UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";
import { handleApiError } from "../../utils/ApiErrorHandler";
import CommonPagination from "../UI/Pagination/commonPagination";
import DataTable from "../UI/DataTable/DataTable";
import FilterButtons from "../UI/FilterButton";
import ErrorFallback from "../Error/ErrorPage";
import Link from "../UI/Link/index";

export interface Variant {
  id: number;
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  product_type: string;
  price: number;
  discounted_price: number;
  image_large_url: string | null;
  image_thumbnail_url: string | null;
  base_product: number;
}
export interface VariantsResponse {
  objects: Variant[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const VariantsList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    //clearAllFilters,
    clearFilter,
  } = useTableFilters();
  const navigate = useNavigate();
  const [variants, setVariants] = useState<Variant[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [search, setSearch] = useState("");
  const [error, setError] = useState<string | null>(null);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const [totalCount, setTotalCount] = useState<number>(0);

  // const [currentPage, setCurrentPage] = useState<number>(1);
  //   const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });

  // const getVariants = async (search: string, page: number) => {
  //   setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/v2/product-variants-list/`,
  //       {
  //         params: { page, search },
  //       }
  //     );
  //     if (response.status === 200) {
  //       setVariants(response.data);
  //     } else {
  //       message.error("Error fetching variants");
  //       setVariants(null);
  //     }
  //   } catch (error) {
  //     message.error("Error fetching variants");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   getVariants(search, currentPage);
  // }, [currentPage]);

  // const showDeleteConfirm = (id: number) => {
  //   setDeleteId(id);
  //   setIsDeleteModalOpen(true);
  // };

  useEffect(() => {
    const controller = new AbortController();
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // setError(null);
        const response = await axiosInstance.get<VariantsResponse>(
          "/api/menu/v2/product-variants-list/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setVariants(response.data.objects);
          setTotalCount(response.data.total_count);
          ////setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        handleApiError(error, setError, navigate);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const initialSearch = params.get("search") || "";
    setSearch(initialSearch);
  }, [location.search]);

  const handleSearchChange = useCallback((value: string) => {
    //console.log(value);
    handleFilterChange("search", value);
    // localStorage.setItem("productSearch", value);
  }, []);

  // const handleSearch = useCallback(() => {
  //   // setCurrentPage(1);
  //   // getVariants(search, 1);
  // }, [search]);

  // const handleClear = useCallback(() => {
  //   // setSearch("");
  //   // setCurrentPage(1);
  //   // getVariants("", 1);
  // }, [setSearch]);

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearch("");
      // localStorage.removeItem("productSearch");
    }
  };

  // const clearAllFiltersHandler = () => {
  //   clearAllFilters();
  //   setSearch("");
  //   //localStorage.removeItem("productSearch");
  // };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const handleDelete = async () => {
    if (!deleteId) return;
    try {
      const response = await axiosInstance.delete(
        `/api/menu/variants/${deleteId}/`
      );
      if (response.status === 204) {
        message.success("Variant deleted successfully");
        // getVariants(search, currentPage);
      } else {
        message.error("Failed to delete variant");
      }
    } catch (error) {
      message.error("Failed to delete variant");
    } finally {
      setIsDeleteModalOpen(false);
      setDeleteId(null);
    }
  };
  const data: Variant[] =
    variants?.map((variant) => ({
      ...variant,
      key: variant.id, // Ensure a key for Ant Design Table
    })) || [];

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (text: string, record: Variant) => (
        // <span onClick={() => navigate(`/variants/${record.id}`)}>
        //   <Link>{text}</Link>
        // </span>
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/variants/${record.id}`}
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "POS",
      dataIndex: "code",
      key: "code",
    },
    // {
    //   title: "SKU",
    //   dataIndex: "sku",
    //   key: "sku",
    // },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
    },
    {
      title: "Product Type",
      dataIndex: "product_type",
      key: "product_type",
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      render: (price: number) => `${price.toFixed(2)}`,
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: Variant) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  if (!loading && error) {
    // console.log("error", error);
    return (
      <>
        <ErrorFallback
          error={error}
          onClicked={() => window.location.reload()}
        />
      </>
    );
  }

  return (
    <div>
      <div className="main-dashboard-buttons">
        <Link to={`./addvariant`}>
          <button className="typography">+ Add New</button>
        </Link>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers d-flex flex-wrap justify-content-between align-items-center">
          <div className="title">Variants</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            {/* <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Code or Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getVariants(search, currentPage)}>
                  Search
                </button>
              </div>
            </div> */}
            <div className="d-flex align-items-center flex-wrap">
              <div>
                <FilterButtons
                  showClearButtons={showClearButtons}
                  appliedFilters={appliedFilters}
                  //clearAllFilters={clearAllFiltersHandler}
                  ClearAllBtnClassName="clear-all-btn"
                  clearFilter={clearFilterHandler}
                  formatFilterValue={formatPaymentMethod}
                  filters={filters}
                  btnClassName="clear-btn"
                />
              </div>
              <div>
                <SearchBox
                  value={search}
                  onChange={setSearch}
                  onSearch={() => handleSearchChange(search)}
                  placeholder="Enter Code or Name"
                  // className="ms-2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <DataTable<Variant>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={false}
          scroll={{ x: 1100, y: 700 }}
        />
      </div>
      <div className="d-flex justify-content-end align-items-center mt-4 mb-4">
        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          showSizeChanger
          onShowSizeChange={handlePageChange}
          onChange={handlePageChange}
        />
      </div>
      <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this variant?</p>
      </Modal>
    </div>
  );
};

export default VariantsList;
