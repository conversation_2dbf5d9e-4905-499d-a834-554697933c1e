{"name": "bk-oms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "uat": "vite --mode uat", "prod": "vite --mode production", "build:dev": "tsc -b && vite build  --mode development", "build:uat": "tsc -b && vite build  --mode uat", "build:prod": "tsc -b && vite build  --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@tailwindcss/vite": "^4.0.14", "@types/js-cookie": "^3.0.6", "antd": "^5.23.0", "array-move": "^4.0.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "react": "^18.3.1", "react-bootstrap": "^2.10.8", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-router-dom": "^7.1.1", "react-sortablejs": "^6.1.4", "sortablejs": "^1.15.6", "tailwindcss": "^4.0.14", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/lodash": "^4.17.16", "@types/react": "^18.3.18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.5", "@types/react-sortable-hoc": "^0.7.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}