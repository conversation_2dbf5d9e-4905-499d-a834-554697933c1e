import React from "react";
import { Button } from "antd";
import { CloseOutlined } from "@ant-design/icons";

export interface FilterButtonsProps {
  showClearButtons: boolean;
  appliedFilters: string[];
  clearAllFilters?: () => void;
  clearFilter: (key: string) => void;
  formatFilterValue: (value: string) => string;
  filters: Record<string, string | undefined>;
  clearButtonText?: string;
  btnClassName?: string;
  ClearAllBtnClassName?: string;
}

const FilterButtons: React.FC<FilterButtonsProps> = ({
  showClearButtons,
  appliedFilters,
  clearAllFilters,
  clearFilter,
  formatFilterValue,
  filters,
  btnClassName,
  ClearAllBtnClassName,
}) => {
  return (
    <div className="d-flex flex-wrap">
      {showClearButtons && clearAllFilters && (
        <Button
          shape="round"
          type="default"
          className={`${ClearAllBtnClassName}`}
          onClick={clearAllFilters}
        >
          Clear All
        </Button>
      )}

      {appliedFilters.map((key) => {
        const filterValue = filters[key];
        const displayValue =
          filterValue === "True"
            ? "Yes"
            : filterValue === "False"
            ? "No"
            : formatFilterValue(filterValue || "");
        return (
          <Button
            shape="round"
            key={key}
            type="link"
            onClick={() => clearFilter(key)}
            className={`filter-btn ${btnClassName}`}
          >
            {displayValue}
            <CloseOutlined />
          </Button>
        );
      })}
    </div>
  );
};

export default FilterButtons;
