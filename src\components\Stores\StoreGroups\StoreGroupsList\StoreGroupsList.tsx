import { useCallback, useEffect, useState } from "react";
import { message, Modal } from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import DataTable from "../../../UI/DataTable/DataTable";
import { axiosInstance } from "../../../../apiCalls";
import LINK from "../../../UI/Link/index";

import "../../../Products/Products.css";
import axios from "axios";
import Link from "../../../UI/Link/index";
import Button from "../../../UI/Button";

export interface StoreGroup {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

const StoreGroupsList: React.FC = () => {
  // const navigate = useNavigate();

  const [storeGroups, setStoreGroups] = useState<StoreGroup[]>([]);
  const [loading, setLoading] = useState(false);
  //   const [search, setSearch] = useState("");

  const fetchStoreGroups = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get("/api/stores/groups/");
      setStoreGroups(response.data || []);
    } catch (error) {
      console.error("Error fetching store groups:", error);
      message.error("Failed to fetch store groups. Please try again.");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStoreGroups();
  }, [fetchStoreGroups]);

  const handleStatusChange = (id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Status" : "Deactivate Status",
      content: `Are you sure you want to ${
        isActive ? "activate" : "deactivate"
      } this status?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          setLoading(true);
          const response = await axiosInstance.patch(
            `/api/stores/groups/${id}/`,
            {
              is_active: isActive,
            }
          );

          if (response.status === 200) {
            message.success(`Status updated successfully.`);
            setStoreGroups((prevData: any) =>
              prevData.map((feature: any) =>
                feature.id === id
                  ? { ...feature, is_active: isActive }
                  : feature
              )
            );
          } else {
            message.error("Failed to update user status.");
          }
        } catch (err: unknown) {
          if (axios.isAxiosError(err)) {
            message.error(
              err.response?.data?.message ||
                "An error occurred while updating user status."
            );
          } else {
            message.error("An unexpected error occurred.");
          }
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // const handleAddNew = useCallback(() => {
  //   navigate("/add-store-group");
  // }, [navigate]);

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (text: string, record: StoreGroup) => (
        // <span onClick={() => navigate(`/store-group-details/${record.id}`)}>
        //   <Link>{text}</Link>
        // </span>
        <>
          {text ? (
            <LINK
              className="common-link text-decoration-none"
              to={`/store-group-details/${record.id}`}
            >
              {text}
            </LINK>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "20%",
      render: (is_active: boolean, record: StoreGroup) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {" "}
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons mt-3">
        <Link to="/add-store-group">
          <Button className="typography">+ Add New</Button>
        </Link>
      </div>

      <div className="store-header">
        <h2>Store Groups</h2>
        {/* <SearchBox
          value={search}
          onChange={setSearch}
          onSearch={() => console.log("searching", search)}
          onClear={() => setSearch("")}
          placeholder="Enter Code or Name"
        /> */}
      </div>

      <DataTable<StoreGroup>
        columns={columns}
        dataSource={storeGroups}
        loading={loading}
        pagination={false}
        scroll={{ x: "max-content" }}
      />
    </div>
  );
};

export default StoreGroupsList;
