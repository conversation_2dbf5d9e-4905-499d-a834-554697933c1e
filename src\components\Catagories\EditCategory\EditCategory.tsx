import React, { useState, useEffect } from "react";
import { Form, Input, InputNumber, Button, message, Switch } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";

const EditCategory: React.FC = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  //const [searchParams] = useSearchParams();
  // const storeId = searchParams.get("Category");
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        const response = await axiosInstance.get(
          `/api/menu/v2/categories/${id}/`
        );
        setInitialValues(response.data);
        form.setFieldsValue(response.data);
      } catch (error) {
        console.error("Error fetching category:", error);
        message.error("Failed to fetch category details.");
      }
    };

    if (id) {
      fetchCategory();
    }
  }, [id, form]);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      const updatedValues = Object.keys(values).reduce((acc, key) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key];
        }
        return acc;
      }, {} as Record<string, any>);

      // If nothing has been changed, inform the user and stop processing
      if (Object.keys(updatedValues).length === 0) {
        message.info("No changes detected.");
        setLoading(false);
        return;
      }

      const response = await axiosInstance.put(
        `/api/menu/v2/categories/${id}/`,
        updatedValues
      );

      if (response.status === 200) {
        message.success("Category Updated Successfully!");
        navigate(`/categories/${id}?name=${encodeURIComponent(values.name)}/`);
      } else {
        message.error("Failed to update category.");
      }
    } catch (error) {
      console.error("Error updating category:", error);
      message.error("Failed to update category.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Edit Category</h3>
      <Form
        form={form}
        name="edit_category"
        layout="vertical"
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item
          label="Name"
          name="name"
          rules={[
            { required: true, message: "Please input the category name" },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="Code"
          name="code"
          rules={[
            { required: true, message: "Please input the category code" },
          ]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item label="Description" name="description">
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          label="Crown Category"
          name="crown_category"
          valuePropName="checked"
        >
          <Switch
            onChange={(checked) => {
              form.setFieldsValue({
                crown_category: checked,
                recommendation_category: checked
                  ? false
                  : form.getFieldValue("recommendation_category"),
              });
            }}
          />
        </Form.Item>

        <Form.Item
          label="Recommendation Category"
          name="recommendation_category"
          valuePropName="checked"
        >
          <Switch
            onChange={(checked) => {
              form.setFieldsValue({
                recommendation_category: checked,
                crown_category: checked
                  ? false
                  : form.getFieldValue("crown_category"),
              });
            }}
          />
        </Form.Item>

        <Form.Item
          label="Position"
          name="position"
          rules={[
            { required: true, message: "Please input the category position" },
          ]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            className="btn-save"
            htmlType="submit"
            loading={loading}
          >
            Update
          </Button>
          <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate(-1)}
          >
            Cancel
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditCategory;
