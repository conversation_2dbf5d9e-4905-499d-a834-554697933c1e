import { useEffect, useState } from "react";
import { Table, Form, Popover, Button, Input } from "antd";

interface DataTableProps<T> {
  title: string;
  fetchData: (searchQuery?: string) => Promise<T[]>;
  columns: any[];
  formFields: { name: string; placeholder: string; type?: string }[];
  onSubmit: (values: any) => Promise<void>;
}

const DataTable = <T extends { id: number; name: string }>({
  title,
  fetchData,
  columns,
  formFields,
  onSubmit,
}: DataTableProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  // const [searchResults, setSearchResults] = useState<T[]>([]);
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    setLoading(true);
    fetchData(searchInput)
      .then((res) => setData(res))
        .catch((err) => console.error(err))
        .finally(() => setLoading(false));
    }, [searchInput]);

  const popoverContent = (
    <div>
      {/* {searchResults.map((result) => (
        <div
          key={result.id}
          onClick={() => {
            setSearchInput(result.name);
            setVisible(false);
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {result.name}
        </div>
      ))} */}
    </div>
  );

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button className="typography"> + Add New </button>
      </div>
      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">{title}</div>
        </div>
        <Form form={form} layout="inline" onFinish={onSubmit}>
          {formFields.map((field) => (
            <Form.Item
              key={field.name}
              name={field.name}
              rules={[
                { required: true, message: `Please input ${field.name}` },
              ]}
            >
              <Input
                type={field.type || "text"}
                placeholder={field.placeholder}
                value={searchInput}
                onChange={(e) => {
                  setSearchInput(e.target.value);
                  setVisible(true);
                  form.setFieldsValue({ [field.name]: e.target.value });
                }}
              />
              {field.name === "product" && (
                <Popover
                  content={popoverContent}
                  title="Search Results"
                  trigger="click"
                  placement="bottom"
                  open={
                    searchInput.length >= 3 &&
                    // searchResults.length > 0 &&
                    visible
                  }
                  onOpenChange={(open) => setVisible(open)}
                ></Popover>
              )}
            </Form.Item>
          ))}
          <Form.Item shouldUpdate>
            {() => (
              <Button className="typography" htmlType="submit">
                Submit
              </Button>
            )}
          </Form.Item>
        </Form>
      </div>
      <div className="pt-4 mt-4">
        <Table<T>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{ pageSize: 5 }}
          scroll={{ x: 800, y: 500 }}
        />
      </div>
    </div>
  );
};

export default DataTable;
