import { Form, InputN<PERSON>ber, But<PERSON>, message, Radio } from "antd";
import { useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";

const EditStoreModifier = () => {
  const [form] = Form.useForm();
  const { modiferid } = useParams();
  const [initialValues, _setInitialValues] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const name = searchParams.get("name");
  const store = searchParams.get("store");
  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // Compare with initial values and filter only changed fields
      const updatedValues = Object.keys(values).reduce((acc, key) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key];
        }
        return acc;
      }, {} as Record<string, any>);

      // If user entered at least one field, allow update
      if (Object.keys(updatedValues).length === 0) {
        message.info("No changes detected.");
        setLoading(false);
        return;
      }

      const response = await axiosInstance.patch(
        `api/menu/v2/update-store-modifiers/${modiferid}/`,
        updatedValues
      );

      if (response.status === 200) {
        message.success("Modifier Updated Successfully!");
        navigate(`/stores/${store}?tab=Store_Modifiers`);
      }
    } catch (error: any) {
      console.error("Error updating variant:", error);
      message.error("Failed to update variant.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <h3>Update {name}</h3>
      <Form
        form={form}
        name="edit_variant"
        layout="vertical"
        onFinish={onFinish}
        className="edit-variant-form"
      >
        <Form.Item
          name="price"

          label="Price"
        >
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        <Form.Item name="is_active" label="Is Active">
          <Radio.Group>
            <Radio value={true}>Active</Radio>
            <Radio value={false}>Inactive</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Update
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditStoreModifier;
