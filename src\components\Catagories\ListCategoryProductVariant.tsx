import React, { useEffect, useRef, useState } from "react";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import { message, Button } from "antd";
import { CheckOutlined, EditOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import DataTable from "../UI/DataTable/DataTable";
import Link from "../UI/Link";

export interface CategoryProductVariant {
  id: number;
  variant_name: string;
  variant_pos_number: string;
  category: number;
  variant: number;
  position: number;
}

interface ListCategoryProductVariantProps {
  categoryName: string | null;
  categoryId: string | null;
}

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}
const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};
interface CategoryWithKey extends CategoryProductVariant {
  key: string;
}

const ListCategoryProductVariant: React.FC<ListCategoryProductVariantProps> = ({
  categoryName,
  categoryId,
}) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [categories, setCategories] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState("");
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteId, setDeleteId] = useState<number | null>(null);
  const [editingCategoryId, _setEditingCategoryId] = useState<number | null>(
    null
  );
  const [dragEnabled, setDragEnabled] = useState(false);
  const [editingId, _setEditingId] = useState<number | null>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);

  const positionInputRef = useRef<HTMLInputElement>(null);

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = categories.findIndex((c) => c.key === active.id);
    const newIndex = categories.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(categories, oldIndex, newIndex);
    setCategories(newOrder);
  };

  // const handleClearSearch = () => {
  //   setSearch("");
  //   handleFilterChange("search", "");
  // };
  const handleSaveNewOrder = async () => {
    try {
      const updatedmodifiers = categories.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));
      const response = await axiosInstance.patch(
        `/api/menu/v2/category-products-position-update/${id}/`,
        {
          list: updatedmodifiers,
        }
      );
      if (response.status === 200) {
        message.success("Product variants reordered successfully.");
        getCategories(search);
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );

  const getCategories = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/list-category-products/${id}/`,
        {
          params: { search },
        }
      );
      if (response.status === 200) {
        const sorted = response.data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);

        setCategories(sorted);
      } else {
        console.log("Error fetching categories", response.status);
        setCategories([]);
      }
    } catch (error) {
      console.error("Error fetching categories", error);
    } finally {
      setLoading(false);
    }
  };

  // const updatePosition = async (categoryId: number, newPosition: number) => {
  //   try {
  //     const response = await axiosInstance.patch(
  //       `/api/menu/v2/category-products/${categoryId}/`,
  //       {
  //         position: newPosition,
  //       }
  //     );
  //     if (response.status === 200) {
  //       message.success("Position updated successfully.");
  //       getCategories(search);
  //     } else {
  //       message.error("Failed to update position.");
  //     }
  //   } catch (error) {
  //     console.error("Error updating position", error);
  //     message.error("Something went wrong.");
  //   } finally {
  //     setEditingId(null);
  //     setEditingPosition(null);
  //   }
  // };

  useEffect(() => {
    getCategories(search);
  }, []);
  useEffect(() => {
    if (editingCategoryId !== null) {
      positionInputRef.current?.focus();
    }
  }, [editingCategoryId]);
  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: "15%",
    },
    {
      title: "POS Number",
      dataIndex: "variant_pos_number",
      key: "variant_pos_number",
      width: "15%",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      key: "variant",
      width: "20%",
      render: (text: string, record: CategoryProductVariant) => (
        // <span
        //   onClick={() =>
        //     navigate(
        //       `/variants/${record.variant}/?categoryId=${categoryId}&categoryName=${categoryName}`
        //     )
        //   }
        // >
        //   <Link>{text}</Link>
        // </span>
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/variants/${record.variant}/?categoryId=${categoryId}&categoryName=${categoryName}`}
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "Variant Name",
      dataIndex: "variant_name",
      key: "variant_name",
      width: "25%",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "20%",
      render: (_: any, record: CategoryProductVariant) => (
        <>
          {record.position}
          <Button
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Button>
          {dragEnabled && (
            <Button
              icon={<CheckOutlined />}
              onClick={() => {
                setDragEnabled(false);
                handleSaveNewOrder();
              }}
            />
          )}
        </>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button className="typography" onClick={() => navigate("./add/")}>
          + Add New
        </button>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Product Variants</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search with Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getCategories(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <DndContext
          sensors={sensors}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={categories.map((item) => item.key)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable
              loading={loading}
              columns={columns}
              dataSource={categories}
              components={{
                body: {
                  row: (props: RowProps) => (
                    <DragRow {...props} dragEnabled={dragEnabled} />
                  ),
                },
              }}
              rowKey="key"
            />
          </SortableContext>
        </DndContext>
      </div>
      {/* <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this Product Variant?</p>
      </Modal> */}
    </div>
  );
};

export default ListCategoryProductVariant;
