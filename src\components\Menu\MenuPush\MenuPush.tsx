import React, { useEffect, useState } from "react";
import { message, Select, Spin, AutoComplete, Card, Form, Tag } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useDebounce } from "../../../customHooks/useDebounce";
import { Button } from "react-bootstrap";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

interface ConfigSetProps {
  id: number;
  name: string;
  config_set_type: string;
}

const MenuPush: React.FC = () => {
  const [storeOptions, setStoreOptions] = useState<SelectProps["options"]>([]);
  const [configOptions, setConfigOptions] = useState<SelectProps["options"]>(
    []
  );
  const [loadingStores, setLoadingStores] = useState(false);
  const [loadingConfigs, setLoadingConfigs] = useState(false);
  const [selectedStores, setSelectedStores] = useState<StoreDataProps[]>([]);
  const [thirdPartyIds, setThirdPartyIds] = useState<string[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<ConfigSetProps | null>(
    null
  );
  const [storeSearchTerm, setStoreSearchTerm] = useState<string>("");
  const [configSearchValue, setConfigSearchValue] = useState<string>("");

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const debouncedStoreSearch = useDebounce(storeSearchTerm, 400);
  const debouncedConfigSearch = useDebounce(configSearchValue, 400);

  const fetchStores = async (search = "") => {
    setLoadingStores(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/${search ? `?search=${search}` : ""}`
      );
      const options = (response.data.objects || []).map(
        (store: StoreDataProps) => ({
          value: store.id,
          label: `${store.name} (${store.code})`,
          third_party_id: store.third_party_id,
        })
      );
      setStoreOptions(options);
    } catch {
      message.error("Failed to load stores");
    } finally {
      setLoadingStores(false);
    }
  };

  const fetchConfigSets = async (search = "") => {
    setLoadingConfigs(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/config-sets/${search ? `?search=${search}` : ""}`
      );
      const options = (response.data?.objects || []).map((config: any) => {
        const cleanedLabel = config.name.replace(/[\[\]']/g, "");
        return {
          value: cleanedLabel.trim(),
          label: cleanedLabel.trim(),
          key: `${config.id}`,
          id: config.id,
          config_set_type: config.config_set_type || "",
        };
      });
      setConfigOptions(options);
    } catch {
      message.error("Failed to load config sets");
      setConfigOptions([]);
    } finally {
      setLoadingConfigs(false);
    }
  };

  const generateMenu = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.post(`api/menu/push-menu/`, {
        stores: thirdPartyIds,
        config_set: selectedConfig?.id,
      });
      if (response.status === 201) {
        setConfigSearchValue("");
        setSelectedConfig(null);
        setSelectedStores([]);
        setThirdPartyIds([]);

        message.success(response.data.message || "Menu pushed successfully");
      } else {
        message.error("Something went wrong while pushing menu");
      }
    } catch (err: any) {
      let finalError = "Unexpected error occurred. Please try again.";
      if (err.response) {
        const data = err.response.data;

        if (typeof data === "object") {
          const primaryError = data.error;
          const nestedError = data.data?.error;

          finalError = [primaryError, nestedError].filter(Boolean).join(": ");
        } else if (typeof data === "string") {
          finalError = data;
        }

        console.error("API Error Response:", data);
      } else if (err.request) {
        console.error("No response received:", err.request);
        finalError = "No response from server. Please try again.";
      } else {
        console.error("Unexpected error:", err.message);
      }

      setError(finalError);
      message.error(finalError);
    } finally {
      setLoading(false);
    }
  };

  const handleStoreSelect = (values: number[]) => {
    const selected = values
      .map((id) => {
        const store = storeOptions?.find((s) => s?.value === id);
        return store
          ? {
              id,
              name: store.label as string,
              code: "",
              third_party_id: store.third_party_id || "",
            }
          : null;
      })
      .filter(Boolean) as StoreDataProps[];

    setSelectedStores(selected);
    setThirdPartyIds(selected.map((store) => store.third_party_id));
  };

  // const handleConfigSelect = (_value: string, option: any) => {
  //   setSelectedConfig({
  //     id: option.value,
  //     name: option.label,
  //     config_set_type: option.config_set_type || "",
  //   });
  //   setConfigSearchValue(option.label);
  // };

  const handleConfigSelect = (_value: string, option: any) => {
    //   console.log("handleConfigSelect called with:", { value, option });

    const label = String(option.label ?? "");
    // console.log("Setting label in input:", label);

    setSelectedConfig({
      id: Number(option.id),
      name: label,
      config_set_type: option.config_set_type || "",
    });
    setError(null);
    // // Value is already the label, so no need to override
    // console.log("Selected config with label as value:", label);
  };

  useEffect(() => {
    fetchStores();
  }, []);

  useEffect(() => {
    if (debouncedStoreSearch.length >= 0) fetchStores(debouncedStoreSearch);
  }, [debouncedStoreSearch]);

  useEffect(() => {
    if (debouncedConfigSearch.length >= 3) {
      fetchConfigSets(debouncedConfigSearch);
    } else {
      setConfigOptions([]);
    }
  }, [debouncedConfigSearch]);

  return (
    <div>
      <Card className="mt-3" title="Push Menu" bordered={true}>
        <Form
          //form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="Select Config Set"
            name="config_set"
            rules={[{ required: true, message: "Please select a config set" }]}
            initialValue={selectedConfig?.name}
          >
            <AutoComplete
              placeholder="Search and select config set"
              value={configSearchValue}
              onSearch={setConfigSearchValue}
              onSelect={handleConfigSelect}
              onClear={() => {
                setSelectedConfig(null);
                setConfigSearchValue("");
                setConfigOptions([]);
                setError(null);
              }}
              style={{ width: "100%", marginBottom: "10px" }}
              options={configOptions}
              notFoundContent={
                loadingConfigs &&
                (configSearchValue.length >= 3 ||
                  configSearchValue.length === 0) ? (
                  <Spin size="small" />
                ) : configSearchValue.length > 0 &&
                  configSearchValue.length < 3 ? (
                  "Type at least 3 characters to search"
                ) : configOptions?.length === 0 ? (
                  "No config sets found"
                ) : null
              }
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="Select Stores"
            name="stores"
            rules={[
              { required: true, message: "Please select at least one store" },
            ]}
            initialValue={selectedStores.map((store) => store.id)}
          >
            <Select
              mode="multiple"
              showSearch
              placeholder="Search and select stores"
              value={selectedStores.map((store) => store.id)}
              notFoundContent={
                loadingStores ? <Spin size="small" /> : "No stores"
              }
              filterOption={false}
              onSearch={setStoreSearchTerm}
              onChange={handleStoreSelect}
              style={{ width: "100%" }}
              options={storeOptions}
            />
          </Form.Item>
          <Form.Item label={null}>
            {error && <Tag className="text-danger fs-6">{error}</Tag>}
          </Form.Item>
          <Form.Item label={null}>
            <div className="d-flex justify-content-end mt-3">
              {/* {selectedStores.length > 0 && selectedConfig && ( */}
              <Button className="btn-save" onClick={generateMenu}>
                {loading ? <Spin size="small" /> : "Push Menu"}
              </Button>
              {/* )} */}
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default MenuPush;
