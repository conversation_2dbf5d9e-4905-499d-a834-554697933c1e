import React, { useEffect, useState, useRef } from "react";
import { Form, Button, message, Popover, Input, InputNumber } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import "../../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../../apiCalls";

const AddProductToCategory: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams() as { id: string };
  const [productId, setProductId] = useState(0);
  const [modGropSearch, setModGropSearch] = useState("");
  const [modGropResults, setModGropResults] = useState([]);
  const [storePopoverVisible, setModGropPopoverVisible] = useState(false);

  // Prevent API call after manual selection
  const skipSearch = useRef(false);

  const fetchModGrops = async () => {
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/search-product-variants/?search=${modGropSearch}`
      );
      if (response.status === 200) {
        setModGropResults(response.data);
      }
    } catch (error) {
      console.error("Error fetching modifier groups", error);
    }
  };

  useEffect(() => {
    if (skipSearch.current) {
      skipSearch.current = false;
      return;
    }

    if (modGropSearch.length >= 3) {
      setModGropPopoverVisible(true);
      fetchModGrops();
    } else {
      setModGropResults([]);
      setModGropPopoverVisible(false);
    }
  }, [modGropSearch]);

  const onFinish = async (values: any) => {
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/create-category-products/${id}/`,
        { variant: productId, position: values.position }
      );
      if (response.status === 201) {
        message.success("Variant Modifier Group Created Successfully!");
        navigate(`/categories/${id}`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create store: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating store:", error);
        message.error("Failed to create store.");
      }
    }
  };

  const modGropPopoverContent = (
    <div>
      {modGropResults.map((modGrop: any) => (
        <div
          key={modGrop.id}
          onMouseDown={() => {
            skipSearch.current = true;
            setModGropSearch(modGrop.name); // Set visible input text
            form.setFieldsValue({ modifier_group: modGrop.id }); // Store ID in form
            setModGropPopoverVisible(false);
            setProductId(modGrop.id); // Save product ID for API call
          }}
          style={{
            cursor: "pointer",
            padding: "5px",
            borderBottom: "1px solid #ddd",
          }}
        >
          {modGrop.name} ({modGrop.code})
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <h3>Add Product Variant to Category</h3>
      <Form
        form={form}
        name="add_store"
        layout="vertical"
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item
          name="modifier_group"
          label="Product Variant"
          rules={[{ required: true, message: "Please enter a Product Variant" }]}
        >
          <Popover
            content={modGropPopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={
              modGropSearch.length >= 3 &&
              modGropResults.length > 0 &&
              storePopoverVisible
            }
          >
            <Input
              placeholder="Search Product Variant with Code or Name"
              value={modGropSearch}
              onChange={(e) => {
                setModGropSearch(e.target.value);
              }}
            />
          </Popover>
        </Form.Item>

        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please select Position" }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProductToCategory;
