// EditStoreData.tsx
import React, { useEffect, useState } from "react";
import { Form, message } from "antd";
import { useParams, useNavigate } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import StoreForm from "./StoreForm/StoreForm";

interface AddStoreProps {
    name: string;
    code: string;
    phone: string;
    postal_code: string;
    address: string;
    latitude: number;
    longitude: number;
    tax_percentage: number;
    third_party_id: string;
    coverage_type: string;
    business: number;
    ato_id:string;
  }

const EditStoreData: React.FC = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const [storeData, setStoreData] = useState<AddStoreProps | null>(null);

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const res = await axiosInstance.get(`api/stores/${id}`);
        setStoreData(res.data);
      } catch {
        message.error("Failed to fetch store details.");
      }
    };
    fetchStore();
  }, [id]);

  const onFinish = async () => {
    try {
      const res = await axiosInstance.patch(`/api/stores/${id}/`, storeData);
      if (res.status === 200) {
        message.success("Store updated successfully!");
    navigate(`/stores/${id}`);
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || "Update failed.");
    }
  };
// const onFinish=()=>{
//     console.log("hi");
// }

  if (!id) return <div>Loading...</div>;

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Edit Store</h2>
      {storeData ? (
        <StoreForm
          form={form}
          initialValues={storeData}
          onValuesChange={(_, allValues) => setStoreData(allValues)}
          onFinish={onFinish}
          isEdit
        />
      ) : (
        <div>Loading...</div>
      )}
    </div>
  );
  
};

export default EditStoreData;
