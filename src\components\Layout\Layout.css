body {
  font-family: "Poppins" !important;
}

/* Layout Container */
.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5; /* Adjust as per your theme */
}

/* Menu Icon in Header */
.menu-icon {
  font-size: 20px;
  cursor: pointer;
  color: white;
}

/* Logout Button */
.logout-button {
  background: transparent;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.logout-button:hover {
  color: #ff8732; /* Hover effect for logout button */
}

/* Layout Body */
.layout-sider {
  transition: all 0.3s ease;
}

.layout-menu {
  border-right: none;
  font-size: 16px;
  padding-top: 16px;
}

/* Active Menu Item */
.layout-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: white;
}

/* Submenu Styles */
.layout-menu .ant-menu-submenu-title {
  font-weight: 500;
}

.layout-menu .ant-menu-submenu-selected {
  color: #1890ff !important;
}

/* Content Section */
.layout-content {
  padding: 16px;
  background: #fff;
  min-height: 100%;
  transition: all 0.3s ease;
}

.ant-layout-sider-trigger {
  display: none;
}

/* Sider Collapsed Width */
.layout-sider .ant-layout-sider-collapsed {
  width: 60px !important;
}

.layout-sider .ant-layout-sider {
  background: #f0f2f5;
  border-radius: 8px;
}
.typography {
  color: #fff;
  text-align: center;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  border-radius: 5px;
  background: #ff8732;
}
img.oms-logo {
  height: 55px;
}

.header-section-s {
  display: flex;
  justify-content: space-between;
  align-items: center !important;
}
ul.ant-menu {
  background: #ff8732;
}
aside.ant-layout-sider .ant-layout-sider-children {
  border-radius: 20px;
  overflow: overlay;
}
aside {
  padding: 10px;
}
.ant-layout-sider-children ul li {
  color: white !important;
}
.ant-menu-submenu-title {
  color: white !important;
}
header.ant-layout-header.layout-header.css-dev-only-do-not-override-7ny38l {
  background: white !important;
  border: 2px solid #efefef !important;
}
header.ant-layout-header.css-7ny38l {
  background: white !important;
  border: 2px solid #efefef !important;
}
:where(.css-7ny38l).ant-layout-header {
  background: white !important;
  border: 2px solid #efefef !important;
}

.logo-section-s svg {
  color: #fa8431;
}
header .logout-button {
  background: #fa8431;
}
.main-dashboard-buttons {
  display: flex;
  justify-content: flex-end;
}
.main-dashboard-buttons button.typography {
  padding: 10px;
  border: none;
  margin-right: 10px;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 14px;
}
ul.ant-menu {
  background: #ff8732;
  font-family: "Poppins";
  font-size: 14px;
}
li.ant-menu-item.ant-menu-item-only-child {
  height: 35px;
  line-height: 35px;
}

.layout-menu .ant-menu-item-selected {
  background-color: #ff7616 !important;
  color: white;
}
.header.products-headers input.search-text {
  background: #ffffff;
  padding: 20px;
  border: none;
  border: 1px solid #dddddd;
  border-radius: 40px;
  font-size: 14px;
}
.header.products-headers .title {
  font-family: "Poppins";
  font-size: 18px;
  font-weight: bold;
}
.table-header .table-cell.table-cell-header {
  background: #ffffff;
}
.table-cell.table-cell-header {
  background: #fbfbfb !important;
  border: 1px solid #efefef94;
}
.logout-text {
  color: red;
}

.logout-icon {
  color: red !important;
}
/* Avatar */
.user-avatar {
  background-color: #ff8732 !important;
  margin-right: 8px;
  flex-shrink: 0;
}

/* Username */
.username {
  font-family: Poppins, sans-serif;
  font-weight: 00;
  flex: 1;
}
/* Dropdown Arrow */
.dropdown-icon {
  margin-left: 20px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.dropdown-icon.rotate {
  transform: rotate(180deg);
}
/* Trigger Button */
.dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  /* border: 1px solid #ff8732; */
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-trigger.active {
  border: none;
}
.logout-dropdown {
  font-size: 18px;
  font-weight: 500;
  margin-left: auto;
  letter-spacing: 0.5px;
  height: 40px;
  line-height: 30px;
  margin-right: 20px;
  max-width: 100%;
}
