import React, {
  useMemo,
  useCallback,
  useState,
  useRef,
  useEffect,
} from "react";
import { ModifierGroupDetailsPageType } from "../../types/Products";
import { InputNumber, message, Modal, Space } from "antd";
import { EditOutlined, CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import "../Products/Products.css";

import { axiosInstance } from "../../apiCalls";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import DataTable from "../UI/DataTable/DataTable";
import showConfirmActionModal from "../UI/PopUpModal";
import Btn from "../UI/Btn";
import StatusToggle from "../UI/ToggleStatus";

//const { Link } = Typography;
interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}
const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};
interface CategoryWithKey extends ModifierGroupDetailsPageType {
  key: string;
}

interface Props {}
const ModifierGroupDetails: React.FC<Props> = () => {
  const { id } = useParams() as { id: string };
  const [loading, setLoading] = useState(false);
  const [_refreshTrigger, setRefreshTrigger] = useState(0);
  const [data, setData] = useState<CategoryWithKey[]>([]);
  const [search, setSearch] = useState("");

  const [dragEnabled, setDragEnabled] = useState(false);
  const [editingId, _setEditingId] = useState<number | null>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);

  const positionInputRef = useRef<HTMLInputElement>(null);

  // single “which row” state
  const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
    null
  );
  // separate edit values
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const [editingPrice, setEditingPrice] = useState<string | null>(null);
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const modifierName = useMemo(
    () => searchParams.get("modifiergroup_name") ?? "",
    [searchParams]
  );

  useEffect(() => {
    if (editingCategoryId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingCategoryId]);

  // Generic patch call

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = data.findIndex((c) => c.key === active.id);
    const newIndex = data.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(data, oldIndex, newIndex);
    setData(newOrder);
  };

  // const handleClearSearch = () => {
  //   setSearch("");
  //   handleFilterChange("search", "");
  // };
  const handleSaveNewOrder = async () => {
    try {
      const updatedmodifiers = data.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));
      const response = await axiosInstance.patch(
        `/api/menu/v2/modifier-variants-position-update/`,
        {
          list: updatedmodifiers,
        }
      );
      if (response.status === 200) {
        message.success("Modifiers reordered successfully.");
        fetchData(search);
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );
  const updateStoreCategory = async (
    category_id: number,
    updates: Partial<{
      position: number;
      price: number;
      is_active: boolean;
      use_inventory_price: boolean;
    }>
  ) => {
    try {
      setLoading(true);
      const { status } = await axiosInstance.patch(
        `/api/menu/v2/modifier-variants_update/${category_id}/`,
        updates
      );
      if (status === 200) {
        message.success("Modifier Variant updated successfully.");

        // Update local state immediately for UI responsiveness
        setData(prevData =>
          prevData.map(item =>
            item.id === category_id
              ? { ...item, ...updates }
              : item
          )
        );

        setRefreshTrigger((p) => p + 1);
        return true;
      } else {
        message.error("Failed to update category.");
        return false;
      }
    } catch (err: any) {
      message.error(
        err.response?.data?.message ||
          "An error occurred while updating category."
      );
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Price submit
  const handleSubmitPrice = async (category_id: number) => {
    if (editingPrice !== null) {
      const ok = await updateStoreCategory(category_id, {
        price: parseFloat(editingPrice),
      });
      if (ok) {
        fetchData(search);
        setEditingCategoryId(null);
        setEditingPrice(null);
      }
    }
  };

  // Position submit
  // const handleSubmitPosition = async (category_id: number) => {
  //   if (editingPosition !== null) {
  //     const ok = await updateStoreCategory(category_id, {
  //       position: editingPosition,
  //     });
  //     if (ok) {
  //       setEditingCategoryId(null);
  //       setEditingPosition(null);
  //     }
  //   }
  // };

  const handleStatusChange = (category_id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Modifier" : "Deactivate Modifier",
      content: isActive
        ? "Are you sure you want to activate this Modifier Group?"
        : "Are you sure you want to deactivate this Modifier Group?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        const success = await updateStoreCategory(category_id, { is_active: isActive });
        if (success) {
          fetchData(search);
        }
      },
    });
  };
  const useInventoryPriceChange = useCallback(
    (category_id: number, isActive: boolean) => {
      Modal.confirm({
        title: isActive
          ? "Activate Inventory Price"
          : "Deactivate Inventory Price",
        content: isActive
          ? "Are you sure you want to activate Inventory Price?"
          : "Are you sure you want to deactivate Inventory Price?",
        okText: "Yes",
        cancelText: "No",
        className: "custom-modal",
        okButtonProps: { className: "custom-modal-ok-button" },
        cancelButtonProps: { className: "custom-modal-cancel-button" },
        onOk: async () => {
          const success = await updateStoreCategory(category_id, {
            use_inventory_price: isActive,
          });
          if (success) {
            fetchData(search);
          }
        },
      });
    },
    []
  );
  useEffect(() => {
    if (editingCategoryId !== null) {
      positionInputRef.current?.focus();
    }
  }, [editingCategoryId]);
  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);

  const handleEditConfirm = () => {
    showConfirmActionModal({
      isActive: true,
      onConfirm: handleSaveNewOrder,
      Text: "Update Modifier Positions",
      entityName: "",
    });
  };

  const handleEditPrice = (id: number) => {
    showConfirmActionModal({
      isActive: true,
      onConfirm: () => handleSubmitPrice(id),
      Text: "Update Modifier Price",
      entityName: "",
    });
  };

  const columns = [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: "5%",
      // render: (text: string) => (
      //   <Link onClick={() => console.log("clicked", text)}>{text}</Link>
      // ),
    },
    {
      title: "POS Number",
      dataIndex: "pos_number",
      key: "pos_number",
      width: "7%",
    },
    { title: "Name", dataIndex: "name", key: "name", width: "10%" },

    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: "12%",
      render: (price: number, record: ModifierGroupDetailsPageType) => {
        const isEditing =
          editingCategoryId === record.id && editingPrice !== null;
        return isEditing ? (
          <>
            <InputNumber
              ref={codeInputRef}
              value={editingPrice}
              onChange={(value) => {
                setEditingPrice(value?.toString() ?? "");
              }}
              onPressEnter={() => handleEditPrice(record.id)}
              disabled={loading}
              style={{ width: 100, marginRight: 8 }}
            />

            <Btn
              icon={<CheckOutlined />}
              onClick={() => handleEditPrice(record.id)}
              loading={loading}
            />
            <Btn
              icon={<CloseOutlined />}
              onClick={() => {
                // setEditingCategoryId(null);
                setEditingPrice(null);
              }}
              style={{ marginLeft: 4 }}
            />
          </>
        ) : (
          <>
            <span>{price}</span>
            <Btn
              type="link"
              onClick={() => {
                setEditingCategoryId(record.id);
                setEditingPrice(price.toString());
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Btn>
          </>
        );
      },
    },

    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "10%",

      render: (_: any, record: ModifierGroupDetailsPageType) => (
        <>
          {record.position}
          <Btn
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Btn>
          {dragEnabled && (
            <>
              <Space>
                <Btn
                  icon={<CloseOutlined />}
                  onClick={() => {
                    setDragEnabled(false);
                    message.info("Drag and drop is now disabled.");
                  }}
                />
                <Btn
                  icon={<CheckOutlined />}
                  onClick={() => {
                    setDragEnabled(false);
                    handleEditConfirm();
                  }}
                />
              </Space>
            </>
          )}
        </>
      ),
    },
    {
      title: "Use Inventory Price ",
      dataIndex: "use_inventory_price",
      key: "use_inventory_price",
      width: "10%",
      // fixed: "right" as "right",
      render: (isActive: boolean, record: ModifierGroupDetailsPageType) => (
        <StatusToggle
          isActive={isActive}
          id={record.id}
          onToggle={(id, newStatus) =>
            useInventoryPriceChange(id, newStatus)
          }
        />
      ),
    },

    // — STATUS COLUMN (unchanged) —
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean, record: ModifierGroupDetailsPageType) => (
        <StatusToggle
          isActive={isActive}
          id={record.id}
          onToggle={(id, newStatus) => handleStatusChange(id, newStatus)}
        />
      ),
    },
  ];

  // const mapData = useCallback(
  //   (resp: any[]): ModifierGroupModifierProps[] =>
  //     resp.map((item) => ({
  //       id: item.id,
  //       name: item.name,
  //       price: item.price,
  //       position: item.position,
  //       default_qty: item.default_qty,
  //       max_qty: item.max_qty,
  //       upsell_to_packaged_version: item.upsell_to_packaged_version,
  //       is_active: item.is_active,
  //       modifier_group: item.modifier_group,
  //       modifier: item.modifier,
  //       use_inventory_price:item.use_inventory_price,
  //     })),
  //   []
  // );

  const fetchData = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/modifier-variants/${id}/`,
        {
          params: { search },
        }
      );
      if (response.status === 200) {
        const sorted = response.data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);

        setData(sorted);
      } else {
        console.log("Error fetching categories", response.status);
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching categories", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(search);
  }, []);

  return (
    // <StoreTabs
    //   id={id}
    //   apiEndpoint="api/menu/v2/modifier-variants"
    //   name={modifierName}
    //   columns={columns}
    //   dataMapper={mapData}
    //   add={`/modifiers-groups/${id}/add/?name=${encodeURIComponent(
    //     modifierName
    //   )}`}
    //   refreshTrigger={refreshTrigger}
    // />
    <div>
      <div className="main-dashboard-buttons">
        <button
          className="typography"
          onClick={() =>
            navigate(
              `/modifiers-groups/${id}/add/?name=${encodeURIComponent(
                modifierName
              )}`
            )
          }
        >
          + Add New
        </button>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Product Variants</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search with Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => fetchData(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <DndContext
          sensors={sensors}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={data.map((item) => item.key)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable
              loading={loading}
              columns={columns}
              dataSource={data}
              components={{
                body: {
                  row: (props: RowProps) => (
                    <DragRow {...props} dragEnabled={dragEnabled} />
                  ),
                },
              }}
              rowKey="key"
            />
          </SortableContext>
        </DndContext>
      </div>
      {/* <Modal
            title="Confirm Deletion"
            open={isDeleteModalOpen}
            onOk={handleDelete}
            onCancel={() => setIsDeleteModalOpen(false)}
            okText="Delete"
            okType="danger"
            cancelText="Cancel"
          >
            <p>Are you sure you want to delete this Product Variant?</p>
          </Modal> */}
    </div>
  );
};

export default React.memo(ModifierGroupDetails);
