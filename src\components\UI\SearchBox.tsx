import React, { FC, KeyboardEvent, useCallback, useRef, useState } from "react";
import { CloseOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Input, InputRef } from "antd";

interface SearchBoxProps {
  value: string;
  onChange: (newValue: string) => void;
  onSearch: () => void;
  onClear?: () => void;
  placeholder?: string;
}

const SearchBox: FC<SearchBoxProps> = ({
  value,
  onChange,
  onSearch,
  onClear,
  placeholder,
}) => {
  const inputRef = useRef<InputRef>(null);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isSearched, setIsSearched] = useState<boolean>(false);

  const triggerSearch = useCallback(() => {
    if (!value.trim()) {
      inputRef.current?.focus();
    } else {
      onSearch();
      setIsSearched(true);
      inputRef.current?.focus();
    }
  }, [value, onSearch]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        triggerSearch();
      }
    },
    [triggerSearch]
  );

  const handleSearchClick = useCallback(() => {
    triggerSearch();
  }, [triggerSearch]);

  const handleClearClick = useCallback(() => {
    if (onClear) {
      onClear();
      setIsSearched(false);
    }
  }, [onClear]);

  return (
    <div className="search-box d-flex flex-wrap align-items-center">
      {value && isSearched && onClear && (
        <Button
          className="clear-btn"
          onClick={handleClearClick}
          icon={<CloseOutlined />}
          type="default"
        >
          {value}
        </Button>
      )}

      <Input
        ref={inputRef}
        className="search-input ms-2"
        placeholder={placeholder}
        value={value}
        onChange={(e) => {
          onChange(e.target.value);
          if (!e.target.value.trim()) setIsSearched(false);
        }}
        onPressEnter={triggerSearch}
        prefix={<SearchOutlined className={isFocused ? "" : "invisible"} />}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
      />

      <button className="search-btn" onClick={handleSearchClick}>
        Search
      </button>
    </div>
  );
};

export default React.memo(SearchBox);
