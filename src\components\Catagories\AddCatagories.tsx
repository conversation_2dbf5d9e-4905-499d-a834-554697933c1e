import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Upload,
  notification,
  InputNumber,
  Switch,
} from "antd";
import { useNavigate } from "react-router-dom";
import { UploadOutlined } from "@ant-design/icons";
import type { RcFile } from "antd/es/upload";
import axios from "axios";
import { axiosInstance } from "../../apiCalls";
import "../Stores/addStore/AddStore.css";

interface AddCategoryProps {
  name: string;
  code: string;
  description: string;
  image_url: string;
  crown_category: boolean;
  recommendation_category: boolean;
  position: number;
}

const AddCategory: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState<{
    image_url?: string;
  }>({});

  const getPresignedUrl = async (file: RcFile) => {
    try {
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      if (
        !response.data.url ||
        !response.data.url.url ||
        !response.data.url.fields
      ) {
        return null;
      }

      return {
        url: response.data.url.url,
        fields: response.data.url.fields,
        key: response.data.url.fields.key,
      };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };
  useEffect(() => {
    console.log(imagePreview);
  }, [imagePreview.image_url]);

  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    fieldType: "image_url"
  ) => {
    setUploading(true);
    try {
      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });

      const s3Key = uploadData.key;
      const fullUrl = `${s3Key}`;

      form.setFieldValue(fieldType, fullUrl);
      setImagePreview((prev) => ({
        ...prev,
        [fieldType]: fullUrl,
      }));
    } catch (error) {
      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (file: RcFile, fieldType: "image_url") => {
    const uploadData = await getPresignedUrl(file);
    if (uploadData) {
      await uploadToS3(uploadData, file, fieldType);
    }
  };

  const getUploadProps = (fieldType: "image_url") => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false;
    },
    showUploadList: false,
  });

  const handleSwitchChange = (field: string, value: boolean) => {
    if (field === "crown_category" && value) {
      form.setFieldsValue({
        crown_category: true,
        recommendation_category: false,
      });
    } else if (field === "recommendation_category" && value) {
      form.setFieldsValue({
        recommendation_category: true,
        crown_category: false,
      });
    } else {
      form.setFieldValue(field, value);
    }
  };

  const onFinish = async (values: AddCategoryProps) => {
    setLoading(true);
    try {
      const response = await axiosInstance.post("api/menu/categories/", {
        ...values,
      });

      if (response.status === 201) {
        message.success("Category Created Successfully!");
        navigate("/categories");
      }
    } catch (error: any) {
      message.error(
        error.response?.data?.message || "Failed to create category"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="add-store-wrapper">
      <h3>Add Category</h3>
      <Form
        form={form}
        name="add_category"
        layout="vertical"
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item
          name="name"
          label="Category Name"
          rules={[{ required: true, message: "Please enter category name" }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="code"
          label="Code"
          rules={[{ required: true, message: "Please enter category code" }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: "Please enter description" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
        >
          <InputNumber />
        </Form.Item>

        <Form.Item
          name="crown_category"
          label="Crown Category"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch
            checked={form.getFieldValue("crown_category")}
            onChange={(value) => handleSwitchChange("crown_category", value)}
          />
        </Form.Item>

        <Form.Item
          name="recommendation_category"
          label="Recommendation Category"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch
            checked={form.getFieldValue("recommendation_category")}
            onChange={(value) =>
              handleSwitchChange("recommendation_category", value)
            }
          />
        </Form.Item>

        <Form.Item label="Upload Image">
          <Upload {...getUploadProps("image_url")}>
            <Button icon={<UploadOutlined />} loading={uploading}>
              Upload Image
            </Button>
          </Upload>
          {imagePreview.image_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_URL}/${
                imagePreview.image_url
              }`}
              alt="Preview"
              style={{ marginTop: 10, maxWidth: "200px", borderRadius: 8 }}
            />
          )}
        </Form.Item>

        <Form.Item name="image_url" hidden>
          <Input />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            className="btn-save"
            htmlType="submit"
            loading={loading}
          >
            Create Category
          </Button>
          <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate("/categories")}
          >
            Cancel
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddCategory;
