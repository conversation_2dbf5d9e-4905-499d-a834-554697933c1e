import React, { useState } from "react";
import { Dropdown, Avatar, MenuProps, notification, message } from "antd";
import {
  AppstoreOutlined,
  DownOutlined,
  LogoutOutlined,
  UserOutlined,
} from "@ant-design/icons";
import Cookies from "js-cookie";
import { Spinner } from "react-bootstrap";

import { axiosInstance } from "../../../apiCalls";
import { LOGIN_PATH, REFRESH_TOKEN } from "../../../Constant";
import { useNavigate } from "react-router-dom";
import { getAccessToken, getRefreshToken, clearTokens } from "../../../utils/tokenUtils";

interface Props {}

const UserDropdown: React.FC<Props> = () => {
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  // const [username, setUsername] = useState<string | null>(null);

  // useEffect(() => {
  //   const first = Cookies.get(FIRST_NAME);
  //   const last = Cookies.get(LAST_NAME);
  //   if (first && last) setUsername(`${first} ${last}`);
  // }, []);

  const handleLogout = async () => {
    try {
      const refreshToken = Cookies.get(REFRESH_TOKEN);
      if (!refreshToken) {
        clearSessionAndRedirect();
        return;
      }

      setLoading(true);
      const response = await axiosInstance.post(
        "api/accounts/logout/",
        { refresh: refreshToken },
        { headers: { "Content-Type": "application/json" } }
      );
      if (response.status === 200) {
        clearSessionAndRedirect();
        notification.success({
          message: "Logout successful",
          description: response.data?.message || "Logged out successfully",
        });
      }
    } catch {
      message.error("Failed to logout. Please try again.");
      clearSessionAndRedirect();
    } finally {
      setLoading(false);
    }
  };

  const clearSessionAndRedirect = () => {
    clearTokens();
    window.location.href = LOGIN_PATH;
  };

  const getCmsBaseUrl = () => {
    // if (window.location.hostname.includes("localhost")) {
    //   return "http://localhost:5173"; // CMS local dev URL
    // }
    const cmsUrl = import.meta.env.VITE_CMS_URL;
    if (!cmsUrl) {
      console.error("VITE_CMS_URL environment variable is not defined");
      return null;
    }
    return cmsUrl;
  };

  const handleSwitchToCMS = () => {
    const token = getAccessToken();
    const refreshToken = getRefreshToken();

    console.log("Switching to CMS...");
    console.log("Current Access Token:", token ? "Present" : "Missing");
    console.log("Current Refresh Token:", refreshToken ? "Present" : "Missing");

    if (!token) {
      message.error("Session expired, please login again.");
      navigate("/");
      return;
    }

    const cmsUrl = getCmsBaseUrl();
    if (!cmsUrl) {
      message.error("CMS URL is not configured. Please contact administrator.");
      return;
    }

    // Redirect to CMS with token as query param - using 'token' parameter name to match AuthRedirect
    // Also pass refresh token if available for better session management
    const redirectUrl = refreshToken
      ? `${cmsUrl}/auth/redirect?token=${encodeURIComponent(token)}&refreshToken=${encodeURIComponent(refreshToken)}`
      : `${cmsUrl}/auth/redirect?token=${encodeURIComponent(token)}`;

    console.log("Redirecting to:", redirectUrl);
    window.location.href = redirectUrl;
  };

  const menuItems: MenuProps["items"] = [
    {
      key: "cms-dashboard",
      label: <span className="logout-text">CMS Dashboard</span>,
      icon: <AppstoreOutlined />,
      onClick: handleSwitchToCMS,
    },
    {
      key: "logout",
      label: (
        <span className="logout-text">
          {loading ? <Spinner className="me-2" /> : null}
          Logout
        </span>
      ),
      icon: <LogoutOutlined className="logout-icon" />,
      onClick: handleLogout,
    },
  ];

  // const formatUsername = (text: string | null) => {
  //   const fallback = Cookies.get(USERNAME) || ADMIN;
  //   if (!text || text.trim() === "") return fallback;
  //   return text
  //     .split(" ")
  //     .map((word) => word[0].toUpperCase() + word.slice(1).toLowerCase())
  //     .join(" ");
  // };

  return (
    <Dropdown
      className="logout-dropdown"
      menu={{ items: menuItems }}
      trigger={["click"]}
      open={visible}
      onOpenChange={setVisible}
    >
      <div className={`dropdown-trigger ${visible ? "active" : ""}`}>
        <Avatar className="user-avatar">
          <UserOutlined />
        </Avatar>
        {/* <span className="username">{formatUsername(username)}</span> */}
        <DownOutlined className={`dropdown-icon ${visible ? "rotate" : ""}`} />
      </div>
    </Dropdown>
  );
};

export default UserDropdown;
