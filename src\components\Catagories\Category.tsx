import { Card, Typography } from "antd";
import ListCategoryProductVariant from "./ListCategoryProductVariant";
// import StoreToCategory from "./StoresToCategory/StoreToCategory";
import AttachedStoreCategory from "./StoresToCategory/AttachedStoreCategory";
import { useParams, useSearchParams } from "react-router-dom";
import BackButton from "../UI/BackButton";
import CategoryDetails from "./CategoryDetails/CategoryDetails";
import { useEffect } from "react";

const { Title } = Typography;

const tabList = [
  { key: "ProductVariantsDetails", tab: "Details" },
  { key: "ProductVariants", tab: "Product Variants" },
  // { key: "AttachToStore", tab: "Attach to Stores" },
  { key: "AttachedStore", tab: "Attached Stores" },
];

const Category = () => {
  const { id } = useParams() || "";
  const [searchParams, setSearchParams] = useSearchParams();
  
  const activeTabKey1 = searchParams.get("tab") || "ProductVariantsDetails";
  const categoryName = decodeURIComponent(
    searchParams.get("name") || "Category Name"
  );

  const contentList: Record<string, React.ReactNode> = {
    ProductVariantsDetails: <CategoryDetails />,
    ProductVariants: (
      <ListCategoryProductVariant
        categoryName={categoryName}
        categoryId={id ?? null}
      />
    ),
    // AttachToStore: <StoreToCategory />,
    AttachedStore: <AttachedStoreCategory />,
  };

  const onTabChange = (key: string) => {
    setSearchParams({ tab: key, name: categoryName });
  };
  useEffect(()=>{
    console.log(id);
  },[id])
  

  return (
    <>
      <div>
        <BackButton to={`/categories`} />
      </div>
      <Title>
        Category <span className="text-base align-middle mx-1">&gt;&gt;</span>
        {categoryName}
      </Title>
      <Card
        style={{ width: "100%" }}
        tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTabChange}
      >
        {contentList[activeTabKey1]}
      </Card>
    </>
  );
};

export default Category;
