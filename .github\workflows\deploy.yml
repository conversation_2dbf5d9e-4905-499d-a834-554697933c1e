# This is a basic workflow to help you get started with Actions

name: Deploy to s3

# Controls when the action will run.
on:
  workflow_dispatch:
  push: 
     branches:
       - dev
  pull_request:
    branches:
      - uat
      - dev
    types:
      - closed

permissions:
  contents: read
  packages: write
  id-token: write

env:
  AWS_REGION: ap-south-1
  AWS_ENV: ${{ github.ref == 'refs/heads/production' && 'prod' || github.base_ref == 'uat' && 'uat' || 'dev' }}
  AWS_ACCOUNT_ID: ${{ github.ref == 'refs/heads/production' && '************' || github.base_ref == 'uat' && '************' || '************' }}

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build-and-deploy-frontend:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v2
      - name: Set environment for DEV
        if: ${{ env.AWS_ACCOUNT_ID == '************' }}
        run: |
          echo "CLOUDFRONT_ID=E6P6CSZRWGM5A" >> $GITHUB_ENV
      #    echo "VITE_API_URL: https://api.oms.burgerkingdev.in" >> $GITHUB_ENV
      - name: Set environment for UAT
        if: ${{ env.AWS_ACCOUNT_ID == '************' }}
        run: |
          echo "CLOUDFRONT_ID=EX5JFP4I3F9W4" >> $GITHUB_ENV
      #    echo "VITE_API_URL: https://api.oms.burgerkinguat.in" >> $GITHUB_ENV 
      - name: Set environment for PRODUCTION
        if: ${{ env.AWS_ACCOUNT_ID == '************' }}
        run: |
          echo "CLOUDFRONT_ID=E3AL32D63SY654" >> $GITHUB_ENV
      #    echo "VITE_API_URL: https://api.oms.burgerkinguat.in" >> $GITHUB_ENV 
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm i --legacy-peer-deps    
      - run: npm run build:${{ env.AWS_ENV }}
        env:
          NODE_ENV: production
          NODE_OPTIONS: --max-old-space-size=4096
       #   VITE_API_URL: ${{ env.VITE_API_URL }}
        # Deploy push to AWS S3
      - name: Configure AWS Credentials for China region audience
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/GitHubActionsOidcRole
          aws-region: ap-south-1
      - name: Deploy to s3
        run: |
          aws s3 sync dist s3://bk-${{ env.AWS_ENV }}-oms-web-${{ env.AWS_ACCOUNT_ID }}-ap-south-1 --delete --region us-east-1
          aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_ID }} --paths "/*"
