// import React from 'react';
import { StoreProductVariantsProps } from "../../../../types/Products";
import { Typography } from "antd";
import { useParams } from "react-router-dom";
import StoreTabs from "../../../../reusableComponents/store/StoreTabs";
// import { useState } from "react";
// import { axiosInstance } from "../../../../apiCalls";

const { Link } = Typography;

const ListingProductVariantStores = () => {
  // const navigate = useNavigate();
  const { variantid } = useParams() as { variantid: string };
  // const { code } = useParams() as { code: string };

  // State for delete confirmation
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteIds, setDeleteIds] = useState<{ parentId: number; childId: number } | null>(null);

  // Function to open the delete confirmation modal
  // const showDeleteConfirm = (parentId: number, childId: number) => {
  //   setDeleteIds({ parentId, childId });
  //   setIsDeleteModalOpen(true);
  // };

  // // Function to handle deletion
  // const handleDelete = async () => {
  //   if (!deleteIds) return;

  //   try {
  //     const response=await axiosInstance.delete(`api/menu/v2/child-variants/${deleteIds.parentId}/${deleteIds.childId}/`)
  //     if(response.status==204){
  //       message.success('successfully deleted child variants please refesh the page')
  //     }else{
  //       message.error("Failed to delete");
  //     }
  //   } catch (error) {
  //     message.error("Failed to delete");
  //   } finally {
  //     setIsDeleteModalOpen(false);
  //     setDeleteIds(null);
  //   }
  // };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      fixed: "left" as "left",
      render: (text: string, _: StoreProductVariantsProps) => (
        <span onClick={() => console.log("Clicked ID")}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Store ",
      dataIndex: "store_name",
      key: "store_name",
    },
    // {
    //   title: "Variant",
    //   dataIndex: "variant",
    //   key: "variant",
    // },
    // {
    //   title: "Category",
    //   dataIndex: "category",
    //   key: "category",
    // },
    // {
    //   title: "Position",
    //   dataIndex: "position",
    //   key: "position",
    // },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: StoreProductVariantsProps) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.parent, record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  const mapData = (response: any): StoreProductVariantsProps[] => {
    if (!Array.isArray(response.objects)) {
      console.error("Invalid API response format:", response);
      return [];
    }

    return response.objects.map((item: any) => ({
      key: item.id,
      id: item.id,
      price: item.price,
      variant: item.variant,
      store_name: item.store_name,
      category: item.category,
      position: item.position,
    }));
  };

  return (
    <>
      <StoreTabs
        id={variantid}
        apiEndpoint="api/menu/v2/store-product-variants"
        name="Stores List"
        columns={columns}
        dataMapper={mapData}
        // add={`/products/${code}/variants/${variantid}/store/add`}
      />

      {/* Delete Confirmation Modal */}
      {/* <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this child variant?</p>
      </Modal> */}
    </>
  );
};

export default ListingProductVariantStores;
