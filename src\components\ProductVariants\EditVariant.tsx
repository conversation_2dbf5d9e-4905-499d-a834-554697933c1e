import React, {  useState } from "react";
import { Form, InputNumber, Button, message, Radio } from "antd";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
// import { axiosInstance } from "../../../apiCalls";

const EditVariant: React.FC = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const storeId = searchParams.get("store")
  const [loading, setLoading] = useState(false);
  const [initialValues, _setInitialValues] = useState<Record<string, any>>({}); // Store initial data

//   useEffect(() => {
//     const fetchVariant = async () => {
//       try {
//         const response = await axiosInstance.get(`api/menu/variants/${variantId}/`);
//         setInitialValues(response.data); // Store initial values for comparison
//         form.setFieldsValue(response.data);
//       } catch (error) {
//         console.error("Error fetching variant details:", error);
//         message.error("Failed to fetch variant details.");
//       }
//     };

//     fetchVariant();
//   }, [form, variantId]);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // Compare with initial values and filter only changed fields
      const updatedValues = Object.keys(values).reduce((acc, key) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key];
        }
        return acc;
      }, {} as Record<string, any>);

      // If user entered at least one field, allow update
      if (Object.keys(updatedValues).length === 0) {
        message.info("No changes detected.");
        setLoading(false);
        return;
      }


      const response = await axiosInstance.patch(
        `api/menu/v2/update-store-product-variants/${id}/`,
        updatedValues
      );

      if (response.status === 200) {
        message.success("Variant Updated Successfully!");
        navigate(`/stores/${storeId}?tab=Inventory`);
      }
      // if(response.status===400){
      //   message.error()
      // }
    } catch (error: any) {
      console.error("Error updating variant:", error);
      message.error("Failed to update variant.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Edit Variant</h3>
      <Form
        form={form}
        name="edit_variant"
        layout="vertical"
        onFinish={onFinish}
        className="edit-variant-form"
      >
        <Form.Item name="price" label="Price">
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        <Form.Item name="discounted_price" label="Discounted Price">
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        <Form.Item name="is_active" label="Is Active">
          <Radio.Group>
            <Radio value={true}>Active</Radio>
            <Radio value={false}>Inactive</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item name="is_available" label="Is Available">
          <Radio.Group>
            <Radio value={true}>Available</Radio>
            <Radio value={false}>Unavailable</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Update
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default EditVariant;
