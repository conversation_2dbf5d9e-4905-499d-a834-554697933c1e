import React, { useState } from "react";
import { Form, InputNumber, Button, message } from "antd";

import { useNavigate, useParams } from "react-router-dom";
import "../../../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../../../apiCalls";

interface AddProductVariantModifierProps {
  product_variant: number;
  position: number;
  modifier: number;
}

const AddProductVariantModifier: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams() as { id: string };
    const {code} =useParams() as {code:string};
  const { variantid } = useParams() as { variantid: string };

  const [storeData, setStoreData] = useState<AddProductVariantModifierProps>({
    position: 0,
    product_variant: 0,
    modifier: 0,
  });

  const onValuesChange = (
    _: Partial<AddProductVariantModifierProps>,
    allValues: AddProductVariantModifierProps
  ) => {
    setStoreData(allValues);
  };

  const onFinish = async () => {
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/product-variant-modifiers/`,
        storeData
      );
      if (response.status === 201) {
        message.success("Variant Modifier Group Created Successfully!");
        navigate(`/products/${id}/${code}/variants/${variantid}?tab=Modifiers`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create store: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating store:", error);
        message.error("Failed to create store.");
      }
    }
  };

  return (
    <div>
      <h3>Add Variant Modifier </h3>
      <Form
        form={form}
        name="add_store"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={storeData}
        className="add-store-form"
      >
        <Form.Item
          name="product_variant"
          label="Product Variant"
          rules={[{ required: true, message: "Please enter Product Variant" }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item>

        <Form.Item
          name="modifier"
          label="Modifier"
          rules={[{ required: true, message: "Please enter Modifier " }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item>

        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please select Position" }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProductVariantModifier;
