import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Spin,
  message,
  Select,
} from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import BackButton from "../../UI/BackButton";
import { ModifierGroupDetailsPageType } from "../../../types/Products";
import { AxiosError } from "axios";

const ModifierGroupEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm<ModifierGroupDetailsPageType>();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    axiosInstance
      .get<ModifierGroupDetailsPageType>(`api/menu/modifier-groups/${id}/`)
      .then((res) => {
        form.setFieldsValue(res.data);
      })
      .catch((error: AxiosError) => {
        if (error.response) {
          switch (error.response.status) {
            case 404:
              message.error("Modifier Group not found (404)");
              break;
            case 500:
              message.error("Server error. Please try again later (500)");
              break;
            default:
              message.error(
                `Error loading data: ${error.response.statusText} (${error.response.status})`
              );
          }
        } else if (error.request) {
          message.error(
            "No response from server. Check your network connection."
          );
        } else {
          message.error(`Error: ${error.message}`);
        }
        console.error(error);
      })
      .finally(() => setLoading(false));
  }, [id, form]);

  const onFinish = async (values: ModifierGroupDetailsPageType) => {
    setSaving(true);
    try {
      await axiosInstance.patch(`api/menu/modifier-groups/${id}/`, values);
      message.success("Modifier Group updated successfully");
      navigate(-1);
    } catch (error: any) {
      if (error.isAxiosError) {
        const axiosErr = error as AxiosError;
        if (axiosErr.response) {
          if (axiosErr.response.status === 400 && axiosErr.response.data) {
            const data = axiosErr.response.data as Record<
              string,
              string[] | undefined
            >;
            const fieldErrors = Object.entries(data).map(([field, errs]) => ({
              name: field as keyof ModifierGroupDetailsPageType,
              errors: errs || ["Invalid value"],
            }));
            form.setFields(fieldErrors);
            return;
          }

          switch (axiosErr.response.status) {
            case 404:
              message.error("Modifier Group not found (404)");
              break;
            case 500:
              message.error("Server error. Please try again later (500)");
              break;
            default:
              message.error(
                `Update failed: ${axiosErr.response.statusText} (${axiosErr.response.status})`
              );
          }
        } else if (axiosErr.request) {
          message.error(
            "No response from server. Check your network connection."
          );
        } else {
          message.error(`Error: ${axiosErr.message}`);
        }
      } else {
        // Non‐Axios error
        message.error(`Unexpected error: ${error.message || error}`);
      }
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-0">
      <div className="d-flex justify-content-between align-items-center">
        <h2 className="mb-4">Edit Modifier Group</h2>
        <BackButton />
      </div>

      <Form
        form={form}
        name="add_modifier_group"
        layout="vertical"
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item
          name="code"
          label="Code"
          rules={[{ required: true, message: "Please enter code" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: "Please enter name" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="display_name"
          label="Display Name"
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item name="description" label="Description" className="form-item">
          <Input.TextArea rows={3} className="input-field" />
        </Form.Item>
        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
          className="form-item"
        >
          <InputNumber min={1} max={100} className="input-field" />
        </Form.Item>
        <Form.Item name="section" label="Sections" className="form-item">
          <Select className="input-field">
            <Select.Option value="customize">Customize</Select.Option>
            <Select.Option value="choose_side">Choose Side</Select.Option>
            <Select.Option value="choose_drink">Choose Drink</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="category" label="Category" className="form-item">
          <Select className="input-field">
            <Select.Option value="addons">Addons</Select.Option>
            <Select.Option value="drinks">Drinks</Select.Option>
            <Select.Option value="make_into_meal">Make Into Meal</Select.Option>
            <Select.Option value="upsize">Upsize</Select.Option>
          </Select>
        </Form.Item>

        {/* <Form.Item
          name="display_type"
          label="Display Type"
          className="form-item"
        >
          <Select className="input-field">
            <Select.Option value="image">Image</Select.Option>
            <Select.Option value="name">Name</Select.Option>
            <Select.Option value="image_and_name">Image and Name</Select.Option>
            <Select.Option value="compact_image_and_name">
              Compact Image and Name
            </Select.Option>
          </Select>
        </Form.Item> */}
        <Form.Item
          name="max_selectable"
          label="Max Selectable"
          rules={[{ required: true, message: "Please enter Max Selectable" }]}
          className="form-item"
        >
          <InputNumber min={1} max={100} className="input-field" />
        </Form.Item>

        {/* <Form.Item name="channels" label="Channels" className="form-item">
          <Select mode="multiple" className="input-field">
            <Select.Option value={1}>Bk dine In</Select.Option>
            <Select.Option value={2}>BK take away</Select.Option>
            <Select.Option value={3}>Channel 3</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="position" label="Position" className="form-item">
          <InputNumber className="input-field" />
        </Form.Item> */}

        <Form.Item name="is_active" label="Active" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item className="form-item">
          <Button
            type="primary"
            htmlType="submit"
            loading={saving}
            className="submit-button"
          >
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ModifierGroupEditPage;
