import React, { useEffect, useState } from "react";
import { StoreMenuProps } from "../../../types";
import { Card } from "antd";
// import { useParams } from "react-router-dom";
// import { axiosInstance } from "../../../apiCalls";
import { useLocation, useParams } from "react-router-dom";


import StoreProductMGroup from "./StoreProductsModifierGroups/StoreProductMGroups";
import StoreProductVariantChildVariants from "./StoreProductChildVariant/StoreProductVariantChildVariants";
import BackButton from "../../UI/BackButton";


const tabList = [
  {
    key: "tab1",
    tab: "Modifier Groups",
  },
  {
    key: "tab2",
    tab: "Child Varaints",
  },
  // {
  //   key: "tab3",
  //   tab: "Child Varaints",
  // },
];

const StoreMenuDetails: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const { storeId } = useParams(); // Access the 'storeid' query parameter
  // const _productId = queryParams.get("productId"); // Access the 'id' query parameter
  const name = queryParams.get("name");
  const [activeTabKey1, setActiveTabKey1] = useState<string>("tab1");
  const [_productDetails, _setProductDetails] = useState<StoreMenuProps | null>(
    null
  );
  // const fetchProduct = async () => {
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/store-products/${storeId}/${productId}`
  //     );
  //     if (response.status === 200) {
  //       setProductDetails(response.data);
  //     } else {
  //       setProductDetails(null);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching menu data", error);
  //   }
  // };
  // useEffect(() => {
  //   fetchProduct();
  // }, []);

  const onTab1Change = (key: string) => {
    setActiveTabKey1(key);
  };

  const contentList: Record<string, React.ReactNode> = {
    // tab1: productDetails ? (
    //   <div>
    //     <p>
    //       <strong>id:</strong> {productDetails.id}
    //     </p>
    //     <p>
    //       <strong>Name:</strong> {productDetails.variant_name}
    //     </p>
    //     <p>
    //       <strong>Price:</strong> {productDetails.price}
    //     </p>
    //     <p>
    //       <strong>Discounted Price:</strong> {productDetails.discounted_price}
    //     </p>
    //     <p>
    //       <strong>product:</strong>
    //       {productDetails.variant}
    //     </p>
    //     <p>
    //       <strong>Is Active:</strong> {productDetails.is_active ? "Yes" : "No"}
    //     </p>
    //   </div>
    // ) : (
    //   <p>Loading...</p>
    // ),
    tab1: <StoreProductMGroup />,
    tab2: <StoreProductVariantChildVariants />,
  };
  useEffect(()=>{
    console.log(storeId);
  },[storeId])

  return (
    <>
    <BackButton to={`/stores/${storeId}?tab=Inventory`} />
      <Card
        style={{ width: "100%" }}
        title={name}
        tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTab1Change}
      >
        {contentList[activeTabKey1]}
      </Card>
      <br />
      <br />
    </>
  );
};

export default StoreMenuDetails;
