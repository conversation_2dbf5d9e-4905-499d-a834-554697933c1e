import { memo } from "react";
import Link from ".";

interface LinkCellProps {
  id: string | number;
  text: string;
  basePath?: string; // default: "."
  className?: string;
}

const LinkCell = memo(
  ({ id, text, basePath = ".", className }: LinkCellProps) => {
    return (
      <Link
        to={`${basePath}/${id}`}
        className={`common-link text-decoration-none ${className ?? ""}`}
      >
        {text}
      </Link>
    );
  }
);

export default LinkCell;
