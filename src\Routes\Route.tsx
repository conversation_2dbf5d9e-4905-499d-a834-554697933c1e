import AddCategory from "../components/Catagories/AddCatagories";
import AddProductToCategory from "../components/Catagories/AddProductToCategory/AddProductToCategory";
import Category from "../components/Catagories/Category";
import EditCategory from "../components/Catagories/EditCategory/EditCategory";
import StoreToCategory from "../components/Catagories/StoresToCategory/StoreToCategory";
import CategoryDeatils from "../components/Category/CategoryDeatils";
import ChildVariantDetails from "../components/ChildVariants/ChildVariantDetails";
import Error404Page from "../components/Error/Pages/Error404";
import MenuCopy from "../components/MenuCopy/MenuCopy";
import AddModifiersModifierGroup from "../components/ModifierGroups/AddModfiersModfierGroup";
import AddModifierGroup from "../components/ModifierGroups/AddModifierGroup";
import ModifierGroupDetailsPage from "../components/ModifierGroups/ModifierGroupDetailsPage";
import ModifierGroupEditPage from "../components/ModifierGroups/ModifierGroupEditPage/ModifierGroupEditPage";
import ModifierGroups from "../components/ModifierGroups/ModifierGroups";
import AddModifier from "../components/Modifiers/AddModfier";
import Modifiers from "../components/Modifiers/Modifiers";
import ModifiersDetails from "../components/Modifiers/ModifiersDetails";
import StoreModifersDetails from "../components/Modifiers/StoreModifiers/StoreModifiersDetails";
import StoreModiferGroup from "../components/Modifiers/StroreModifierGroup/StoreModiferGroup";
import OrderDetails from "../components/Orders/OrderDetails";
import OrdersList from "../components/Orders/OrdersList";
import AddProduct from "../components/Products/AddProduct";
import ProductDetails from "../components/Products/ProductDetails";
import Products from "../components/Products/Products";
import AddProductVariant from "../components/Products/Variants/AddProductVariant";
import AddProductVariantChildVariant from "../components/Products/Variants/Child Varinants/AddProductVariantChildVariant";
import AddProductVariantModifierGroup from "../components/Products/Variants/Modifier Groups/AddProductVariantModifierGroup";
import AddProductVariantModifier from "../components/Products/Variants/Modifiers/AddProductVariantModifier";
import ProductVariantDetails from "../components/Products/Variants/ProductVariantDetails";
import AddStoreProductVariants from "../components/Products/Variants/Stores/AddStoreProductVariants";
import AddProductVariants from "../components/ProductVariants/AddProductVariants";
import EditVariant from "../components/ProductVariants/EditVariant";
import MasterLevelVariantEdit from "../components/ProductVariants/MasterLevelVariantEdit";
import VariantsList from "../components/ProductVariants/VariantsList";
import AddStore from "../components/Stores/addStore/AddStore";
import EditStoreData from "../components/Stores/EditStoreData";
import AddStoreCategory from "../components/Stores/StoreCategory/AddStoreCatagory";
import StoreDetails from "../components/Stores/StoreDetails";
import AddStoreGroup from "../components/Stores/StoreGroups/AddStoreGroup/AddStoreGroup";
import EditStoreGroup from "../components/Stores/StoreGroups/EditStoreGroup/EditStoreGroup";
import MapGroupToStores from "../components/Stores/StoreGroups/MapGroupToStores/MapGroupToStores";
import StoreGroupDetails from "../components/Stores/StoreGroups/StoreGroupDeatils/StoreGroupDetails";
import StoreGroupsList from "../components/Stores/StoreGroups/StoreGroupsList/StoreGroupsList";
import StoreList from "../components/Stores/StoreList";
import AddStoreProduct from "../components/Stores/storeMenu/AddStoreProduct";
import StoreMenuDetails from "../components/Stores/storeMenu/StoreMenuDetails";
import StoreProductMGroup from "../components/Stores/storeMenu/StoreProductsModifierGroups/StoreProductMGroups";
import AddStoreModifiers from "../components/Stores/StoreModifiers/AddStoreModfiers";
import EditStoreModifier from "../components/Stores/StoreModifiers/EditStoreModifier";
import AddStoreModifierGroup from "../components/Stores/StroreModiferGroups/AddStoreModifierGroup";
import CatagoriesList from "../components/Catagories/CatagoriesList";
import "../App.css";
import SingleMenuPushToStores from "../components/Menu/MenuPush/SingleMenuPushToStores/SingleMenuPushToStores";

export const routes = [
  // Modifiers
  { path: "modifiers/", element: <Modifiers /> }, //<Route path="modifiers/" element={<Modifiers />} />
  { path: "modifiers/:id/", element: <ModifiersDetails /> }, // <Route path="modifiers/:id/" element={<ModifiersDetails />} />

  { path: "modifiers/addmodifier/", element: <AddModifier /> },

  // Modifier Groups
  { path: "modifiers-groups/", element: <ModifierGroups /> },
  { path: "modifiers-groups/addmodifiergroup/", element: <AddModifierGroup /> },
  { path: "modifiers-groups/:id/", element: <ModifiersDetails /> },
  { path: "modifiers-groups/:id/add", element: <AddModifiersModifierGroup /> },
  {
    path: "modifiers-groups-details/:id/",
    element: <ModifierGroupDetailsPage />,
  },
  { path: "modifiers-groups-edit/:id/", element: <ModifierGroupEditPage /> },

  // Orders
  { path: "orders/", element: <OrdersList /> },
  { path: "orders/:id/", element: <OrderDetails /> },

  // Products
  { path: "products/", element: <Products /> },
  { path: "products/addproduct/", element: <AddProduct /> },
  { path: "products/details/:code", element: <ProductDetails /> },
  { path: "products/:code/variants/add", element: <AddProductVariant /> },
  {
    path: "products/:id/:code/variants/modifier/:variantid/add/",
    element: <AddProductVariantModifier />,
  },
  {
    path: "products/:code/variants/:variantId/store/add",
    element: <AddStoreProductVariants />,
  },
  {
    path: "products/variants/:variantId/childvariant/add",
    element: <AddProductVariantChildVariant />,
  },

  // Store Modifier Details
  {
    path: "store/:storeId/modifier-group/:modifierGroupId/",
    element: <StoreModifersDetails />,
  },
  {
    path: "store/:storeId/product-modifier-groups/:id/",
    element: <StoreProductMGroup />,
  },

  // Store Groups
  { path: "store-groups/", element: <StoreGroupsList /> }, // <Route path="store-groups/" element={<StoreGroupsList />} />
  { path: "add-store-group/", element: <AddStoreGroup /> }, // <Route path="add-store-group/" element={<AddStoreGroup />} />
  { path: "edit-store-group/:id", element: <EditStoreGroup /> }, // <Route path="edit-store-group/:id" element={<EditStoreGroup />} />
  { path: "map-group-to-stores/:id", element: <MapGroupToStores /> }, // <Route path="map-group-to-stores/:id" element={<MapGroupToStores />} />
  { path: "store-group-details/:id/", element: <StoreGroupDetails /> }, // <Route path="store-group-details/:id/" element={<StoreGroupDetails />} />

  // Stores
  { path: "stores/", element: <StoreList /> }, // <Route path="stores/" element={<StoreList />} />
  { path: "stores/:id/", element: <StoreDetails /> }, //  <Route path="stores/:id/" element={<StoreDetails />} />
  { path: "stores/addstore/", element: <AddStore /> }, // <Route path="stores/addstore/" element={<AddStore />} />
  { path: "stores/:id/edit", element: <EditStoreData /> }, // <Route path="stores/:id/edit" element={<EditStoreData />} />
  { path: "stores/addstoreProduct/:id/", element: <AddStoreProduct /> }, //
  { path: "stores/addstoremodifier/:id/", element: <AddStoreModifiers /> },
  { path: "stores/categories/add/:id/", element: <AddStoreCategory /> },
  { path: "stores/:id/category/", element: <CategoryDeatils /> },
  { path: "stores/:id/child-variant/", element: <ChildVariantDetails /> },
  { path: "stores/:id/menu/", element: <StoreMenuDetails /> },
  { path: "stores/:id/modfier-groups/", element: <StoreModiferGroup /> },
  {
    path: "stores/modfier-groups/add/:id/",
    element: <AddStoreModifierGroup />,
  },
  {
    path: "stores/store-modifer/update/:modiferid",
    element: <EditStoreModifier />,
  },
  {
    path: "store/:storeId/product-variant-details/:id",
    element: <StoreMenuDetails />,
  },

  // Categories
  { path: "categories/", element: <CatagoriesList /> }, //<Route path="categories/" element={<CatagoriesList />} />
  { path: "categories/addcategory/", element: <AddCategory /> }, // <Route path="categories/addcategory/" element={<AddCategory />} />
  { path: "categories/:id", element: <Category /> }, // <Route path="categories/:id" element={<Category />} />
  { path: "categories/:id/add/", element: <AddProductToCategory /> }, //
  { path: "categories/edit/:id", element: <EditCategory /> },
  { path: "categories/:id/setstores/", element: <StoreToCategory /> },

  // Variants
  { path: "variants", element: <VariantsList /> },
  { path: "variants/:variantid/", element: <ProductVariantDetails /> },
  { path: "variants/addvariant", element: <AddProductVariants /> },
  {
    path: "variants/modifier-groups/:variantid/add/",
    element: <AddProductVariantModifierGroup />,
  },
  {
    path: "variant/:id/EditProductVariant",
    element: <MasterLevelVariantEdit />,
  },
  { path: "variant/edit/:id", element: <EditVariant /> },

  // Menu
  { path: "menu-copy", element: <MenuCopy /> },
  // { path: "generate-menu", element: <MenuGenerate /> },
  // { path: "menu-push", element: <MenuPush /> },
  { path: "bulk-menu-push-to-stores", element: <SingleMenuPushToStores /> },

  // 404 Page
  { path: "*", element: <Error404Page /> },
];
