import React, { useEffect, useState } from "react";
import { Form, InputNumber, Button, message,Popover, Input } from "antd";

import { useNavigate, useParams } from "react-router-dom";
import "../../../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../../../apiCalls";

// interface AddProductVariantProps {
//   position: number;
//   product_variant: number;
//   modifier_group: number;
// }

const AddProductVariantModifierGroup: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  // const { id } = useParams() as { id: string };
  // const { code } = useParams() as { code: string };
  const { variantid } = useParams() as { variantid: string };

  // const [storeData, setStoreData] = useState<AddProductVariantProps>({
  //   position: 0,
  //   product_variant: 0,
  //   modifier_group: 0,
  // });
  const [modGropSearch, setModGropSearch] = useState("");
  const [modGropResults, setModGropResults] = useState([]);
  const [storePopoverVisible, setModGropPopoverVisible] = useState(false);
  // const onValuesChange = (
  //   _: Partial<AddProductVariantProps>,
  //   allValues: AddProductVariantProps
  // ) => {
  //   setStoreData(allValues);
  // };
  const fetchModGrops = async () => {
    try {
      const response = await axiosInstance.get(`api/menu/modifier-groups/?search=${modGropSearch}`);
      if (response.status === 200) {
        setModGropResults(response.data.objects);
      }
    } catch (error) {
      console.error("Error fetching stores", error);
    }
  };

  useEffect(() => {
    if (modGropSearch.length >= 3) fetchModGrops();
  }, [modGropSearch]);

  const onFinish = async (values:any) => {
    // console.log(values)
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/product-variant-modifier-groups/`,
        {...values,product_variant:variantid}
      );
      if (response.status === 201) {
        message.success("Variant Modifier Group Created Successfully!");
        navigate(`/variants/${variantid}?tab=ModifierGroups`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create store: ${error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating store:", error);
        message.error("Failed to create store.");
      }
    }
  };
  const modGropPopoverContent = (
    <div>
      {modGropResults.map((modGrop:any) => (
        <div
          key={modGrop?.id}
          onClick={() => {
            setModGropSearch(modGrop?.name);
            // setModifierData((prev) => ({
            //   ...prev,
            //   modifier: store.id,
            // }));
            form.setFieldsValue({ modifier_group: modGrop.id }); // ✅ Update form value explicitly
            setModGropPopoverVisible(false);
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {modGrop?.name}
        </div>
      ))}
    </div>
  );
  return (
    <div>
      <h3>Add Variant Modifier Group</h3>
      <Form
        form={form}
        name="add_store"
        layout="vertical"
        onFinish={onFinish}
        // onValuesChange={onValuesChange}
        // initialValues={storeData}
        className="add-store-form"
      >
        <Form.Item
          name="modifier_group"
          label="Modifier Group"
          rules={[{ required: true, message: "Please enter a modifier group" }]}
        >
          <Popover
            content={modGropPopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={modGropSearch.length >= 3 && modGropResults.length > 0 && storePopoverVisible}
            onOpenChange={(open) => setModGropPopoverVisible(open)}
          >
            <Input
              placeholder="Search Modifier Group"
              value={modGropSearch}
              onChange={(e) => {
                setModGropSearch(e.target.value);
                setModGropPopoverVisible(true);
              }}
            />
          </Popover>
        </Form.Item>
        {/* <Form.Item
          name="product_variant"
          label="Product Variant"
          rules={[{ required: true, message: "Please enter Product Variant" }]}
          className="form-item"
        >
           <InputNumber min={0} className="input-field" />
        </Form.Item> */}

        {/* <Form.Item
          name="modifier_group"
          label="Modifier Group"
          rules={[{ required: true, message: "Please enter Modifier Group" }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item> */}

        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please select Position" }]}
          className="form-item"
        >
          <InputNumber min={0} className="input-field" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProductVariantModifierGroup;
