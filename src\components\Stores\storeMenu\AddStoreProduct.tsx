import React, { useEffect, useState } from "react";
import { Form, InputNumber, Button, Select, message, Switch } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import "../addStore/AddStore.css";
import { axiosInstance } from "../../../apiCalls";

const AddStoreProduct: React.FC = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<{ id: number; name: string }[]>([]);

  useEffect(() => {
    // Fetch tags from the API
    const fetchTags = async () => {
      try {
        const response = await axiosInstance.get("api/menu/tags/");
        setTags(response.data.objects); // Assuming response.data is an array of { id, name }
      } catch (error) {
        console.error("Error fetching tags:", error);
        message.error("Failed to fetch tags.");
      }
    };
    fetchTags();
  }, []);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      const payload = {
        ...values,
        tags: values.tags || [], // Ensuring tags is always an array
        store: Number(id),
      };

      const response = await axiosInstance.post(
        `api/menu/store-products/${id}/`,
        payload
      );
      if (response.status === 201) {
        message.success("Store Product Created Successfully!");
        navigate(`/stores/${id}`);
      }
    } catch (error: any) {
      console.error("Error creating store product:", error);
      message.error("Failed to create store product.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Add Store Product</h3>
      <Form
        form={form}
        name="add_store_product"
        layout="vertical"
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item
          name="price"
          label="Price"
          rules={[{ required: true, message: "Please enter price" }]}
        >
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        <Form.Item name="discounted_price" label="Discounted Price">
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        <Form.Item name="is_active" label="Is Active" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item
          name="product"
          label="Product ID"
          rules={[{ required: true, message: "Please enter product ID" }]}
        >
          <InputNumber min={1} className="input-field" />
        </Form.Item>

        <Form.Item
          name="base_product"
          label="Base Product ID"
          rules={[{ required: true, message: "Please enter base product ID" }]}
        >
          <InputNumber min={1} className="input-field" />
        </Form.Item>

        <Form.Item name="tags" label="Tags">
          <Select
            mode="multiple"
            placeholder="Select tags"
            className="input-field"
          >
            {tags.map((tag) => (
              <Select.Option key={tag.id} value={tag.id}>
                {tag.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddStoreProduct;
