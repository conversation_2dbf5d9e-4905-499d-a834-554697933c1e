import { useState, useEffect } from "react";
import { Table } from "antd";
import { axiosInstance } from "../../apiCalls";
import Link from "../../components/UI/Link";

interface StoreTabsProps<T> {
  id: string;
  apiEndpoint: string;
  name: string;
  add?: string;
  columns: any[];
  dataMapper: (data: any) => T[];
  sendIdInBody?: boolean;
  refreshTrigger?: number;
}

const StoreTabs = <T extends { id: string | number }>({
  id,
  name,
  apiEndpoint,
  columns,
  dataMapper,
  add,
  sendIdInBody = false,
  refreshTrigger,
}: StoreTabsProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [search, setSearch] = useState("");
  const [totalCount, setTotalCount] = useState<number>(0); // Store total count of items
  // const navigate = useNavigate();

  const fetchData = async (page: number, search: string) => {
    setLoading(true);
    try {
      const url = sendIdInBody ? apiEndpoint : `${apiEndpoint}/${id}/`;

      const requestOptions = sendIdInBody
        ? { page, search, store_id: id } // Send store_id in body
        : { page, search };

      const response = await axiosInstance.get(url, {
        params: requestOptions,
      });

      if (response.status === 200) {
        setData(dataMapper(response.data));
        setTotalCount(response.data.total_count); // Assuming the API returns the total count
      } else {
        setData([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching data", error);
      setData([]);
      setTotalCount(0);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData(currentPage, search);
  }, [currentPage, refreshTrigger]);

  return (
    <div>
      {add && (
        <div className="main-dashboard-buttons">
          <Link to={add}>
            <button className="typography">+ Add New</button>
          </Link>
        </div>
      )}

      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">{name}</div>
          <div className="search-container">
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(currentPage, search)}>
                Search
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="pt-4 mt-4">
        <Table<T>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{
            current: currentPage,
            total: totalCount || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
          }}
          scroll={{ x: 1100 }}
        />
      </div>
    </div>
  );
};

export default StoreTabs;
