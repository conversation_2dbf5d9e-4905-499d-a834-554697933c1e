import React, { createContext, useContext, useState, ReactNode, useMemo } from "react";

interface ProductContextType {
  productId: number | null;
  setProductId: (id: number | null) => void;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const ProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
   const [productId, setProductId] = useState<number | null>(null);

  const value = useMemo(() => ({ productId, setProductId }), [productId]);

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};

export const useProduct = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProduct must be used within a ProductProvider");
  }
  return context;
};
