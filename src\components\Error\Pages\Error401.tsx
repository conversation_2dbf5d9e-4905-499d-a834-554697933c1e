import { <PERSON><PERSON>, <PERSON> } from "antd";
import { Con<PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import Cookies from "js-cookie";
import { useEffect } from "react";
import { message } from "antd";

const Error401: React.FC = () => {
  // const navigate = useNavigate();

  useEffect(() => {
    const token = Cookies.get("token");
    const refreshToken = Cookies.get("refreshToken");

    // Only navigate if tokens are missing
    if (!token || !refreshToken) {
      // Slight delay ensures mount is stable
      setTimeout(() => {
        window.location.pathname = "/";
      }, 0);
    }
  }, []); // Remove navigate from dependency array

  const handleClick = () => {
    const isAuthenticated = Cookies.get("token") && Cookies.get("refreshToken");

    if (isAuthenticated) {
      Cookies.remove("token");
      Cookies.remove("refreshToken");
      window.location.pathname = "/";
    } else {
      console.warn("No active session found.");
      message.error("No active session found.");
    }
  };

  return (
    <Container
      fluid
      className="d-flex align-items-center justify-content-center bg-white vh-100 p-4"
    >
      <Row className="justify-content-center w-100">
        <Col md={6} className="d-flex justify-content-center">
          <div className="bg-light p-5 mt-4 rounded shadow text-center w-120">
            {/* SVG icon */}
            <div className="d-flex justify-content-center align-items-center">
              <svg
                width="100"
                height="100"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#ff4d4f"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                aria-hidden="true"
                focusable="false"
                className="mb-4"
              >
                <rect x="3" y="11" width="20" height="11" rx="2" ry="2" />
                <path d="M7 11V7a5 5 0 0 1 10 0v4" />
              </svg>
            </div>

            <h2 className="fw-bold text-danger mb-3">401 - Unauthorized</h2>
            <p className="text-secondary fs-5 mb-4">
              You are not authorized to access this page. Please log in to
              continue.
            </p>

            <Button
              type="primary"
              className="w-50 mb-3 bg-danger text-white"
              shape="round"
              onClick={handleClick}
            >
              Go to Login Page
            </Button>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default Error401;
