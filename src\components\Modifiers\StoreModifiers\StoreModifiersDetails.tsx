import React, { useCallback, useEffect, useState } from "react";
import {
  Card,
  Spin,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  message,
} from "antd";
import { EditOutlined, SaveOutlined, CloseOutlined } from "@ant-design/icons";
import { useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import StoreModiferGroup from "../StroreModifierGroup/StoreModiferGroup";
import BackButton from "../../UI/BackButton";
import axios, { AxiosError } from "axios";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "Modifiers", tab: "Modifiers" },
];

export interface StoreModifiersDetailsType {
  id: number;
  section: string;
  position: number;
  is_active: boolean;
  store: number;
  max_selectable: number;
}

const StoreModifersDetails: React.FC = () => {
  const { storeId, modifierGroupId } = useParams<{
    storeId: string;
    modifierGroupId: string;
  }>();
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});
  const [searchParams, setSearchParams] = useSearchParams();

  const modifierName = searchParams.get("name") || "";
  const activeTabKey = searchParams.get("tab") || "Details";

  const [storeDetails, setStoreDetails] =
    useState<StoreModifiersDetailsType | null>(null);
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const [form] = Form.useForm();

  const fetchStore = useCallback(async () => {
    try {
      setLoading(true);
      const { data, status } =
        await axiosInstance.get<StoreModifiersDetailsType>(
          `api/menu/store-modifier-groups-details/${storeId}/${modifierGroupId}/`
        );
      if (status === 200) {
        setStoreDetails(data);
        const normalized = {
          section: data.section,
          position: data.position,
          maxSelectable: data.max_selectable,
          isActive: data.is_active,
        };
        setInitialValues(normalized);
        form.setFieldsValue({
          section: data.section,
          position: data.position,
          maxSelectable: data.max_selectable,
          isActive: data.is_active,
        });
      } else {
        setStoreDetails(null);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load modifier-group details");
    } finally {
      setLoading(false);
    }
  }, [storeId, modifierGroupId, form]);

  useEffect(() => {
    fetchStore();
  }, [fetchStore]);

  const onTabChange = (key: string) => {
    const params = new URLSearchParams();
    if (modifierName) params.set("name", modifierName);
    if (key !== "Details") params.set("tab", key);
    setSearchParams(params, { replace: true });
    setIsEditing(false);
  };

  const handleFormSubmit = async (values: any) => {
    try {
      setLoading(true);
      const updatedValues = Object.keys(values).reduce((acc, key) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key];
        }
        return acc;
      }, {} as Record<string, any>);

      // If nothing has been changed, inform the user and stop processing
      if (Object.keys(updatedValues).length === 0) {
        message.info("No changes detected.");
        setLoading(false);
        return;
      }

      const response = await axiosInstance.patch(
        `/api/menu/store-modifier-groups-details/${storeId}/${modifierGroupId}/`,
        {
          section: values.section,
          position: values.position,
          max_selectable: values.maxSelectable,
          is_active: values.isActive,
        }
      );
      if (response.status === 200) {
        message.success("Modifier-group updated");
        setIsEditing(false);
        fetchStore();
      }
    } catch (err: unknown) {
      console.error("Update failed:", err);

      let errorMsg = "Unknown error";
      if (axios.isAxiosError(err)) {
        const axiosErr = err as AxiosError<{ detail?: string }>;

        if (axiosErr.response?.data?.detail) {
          errorMsg = axiosErr.response.data.detail;
        } else if (axiosErr.response?.data) {
          errorMsg = JSON.stringify(axiosErr.response.data);
        } else {
          errorMsg = axiosErr.message;
        }
      } else if (err instanceof Error) {
        errorMsg = err.message;
      }

      message.error(`Update failed: ${errorMsg}`);
    } finally {
      setLoading(false);
    }
  };

  const contentList: Record<string, React.ReactNode> = {
    Details: storeDetails ? (
      isEditing ? (
        <Form
          form={form}
          layout="vertical"
          className="add-store-form"
          onFinish={handleFormSubmit}
        >
          <Card title="Edit Modifier Group" bordered={false} hoverable={false}>
            <Form.Item
              label="Section"
              name="section"
              rules={[{ required: true }]}
              className="form-item"
            >
              <Input style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item
              label="Position"
              name="position"
              rules={[{ required: true }]}
            >
              <InputNumber style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item
              label="Max Selectable"
              name="maxSelectable"
              rules={[{ required: true }]}
            >
              <InputNumber style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item label="Status" name="isActive" valuePropName="checked">
              <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                className="btn-save"
                htmlType="submit"
                icon={<SaveOutlined />}
              >
                {loading ? "Saving…" : "Save"}
              </Button>
              <Button
                className="btn-cancel"
                icon={<CloseOutlined />}
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </Button>
            </Form.Item>
          </Card>
        </Form>
      ) : (
        <div className="flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Modifier Group Details</h2>
            <button
              className="text-white px-4 py-2 rounded transition"
              style={{ backgroundColor: "#FF8732" }}
              onClick={() => setIsEditing(true)}
              onMouseOver={(e) =>
                (e.currentTarget.style.backgroundColor = "#e86f1a")
              }
              onMouseOut={(e) =>
                (e.currentTarget.style.backgroundColor = "#FF8732")
              }
            >
              <EditOutlined /> Edit
            </button>
          </div>
          <div className="space-y-2">
            <p>
              <strong>ID:</strong> {storeDetails.id}
            </p>
            <p>
              <strong>Section:</strong> {storeDetails.section}
            </p>
            <p>
              <strong>Position:</strong> {storeDetails.position}
            </p>
            <p>
              <strong>Max Selectable:</strong> {storeDetails.max_selectable}
            </p>
            <p>
              <strong>Status:</strong>{" "}
              {storeDetails.is_active ? "Active" : "Inactive"}
            </p>
          </div>
        </div>
      )
    ) : (
      <p>Loading…</p>
    ),
    Modifiers: <StoreModiferGroup />,
  };

  // While loading initially (and not editing)
  if (loading && !isEditing) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <BackButton />
      <Card
        className="w-full"
        title={<h3>{modifierName || storeDetails?.section}</h3>}
        tabList={tabList}
        activeTabKey={activeTabKey}
        onTabChange={onTabChange}
      >
        {contentList[activeTabKey]}
      </Card>
    </>
  );
};

export default StoreModifersDetails;
