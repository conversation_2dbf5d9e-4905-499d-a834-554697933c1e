import React, { useCallback, useEffect, useMemo, useState } from "react";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import { message, Modal } from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import axios from "axios";
import Link from "../UI/Link";
import Button from "../UI/Button";
import SearchBox from "../UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";
import DataTable from "../UI/DataTable/DataTable";

export interface ModifierGroup {
  id: number;
  code: string;
  name: string;
  display_name: string;
  description: string;
  is_active: boolean;
  sections: string;
  category: string;
  display_type: string;
  is_required: boolean;
  channels: string[];
}

export interface ModifierGroupsResponse {
  objects: ModifierGroup[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const ModifierGroups: React.FC = () => {
  // const navigate = useNavigate();
  const [modifierGroups, setModifierGroups] =
    useState<ModifierGroupsResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [search, setSearch] = useState("");

  const {
    currentPage,
    pageSize,
    filters,
    handlePageChange,
    handleFilterChange,
  } = useTableFilters();

  const memoizedFilters = useMemo(() => filters, [filters]);

  const getModifierGroups = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(`api/menu/modifier-groups/`, {
        params: { page: currentPage, page_size: pageSize, ...memoizedFilters },
      });
      if (response.status === 200) {
        setModifierGroups(response.data);
        setTotalCount(response.data.total_count);
      } else {
        console.error("Error fetching modifier groups", response.status);
        setModifierGroups(null);
      }
    } catch (error) {
      console.error("Error fetching modifier groups", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getModifierGroups();
  }, [currentPage, pageSize, memoizedFilters]);

  const updateStoreCategory = async (
    modifierGroup_id: number,
    is_active: boolean
  ) => {
    try {
      setLoading(true);
      const response = await axiosInstance.patch(
        `api/menu/modifier-groups/${modifierGroup_id}/`,
        {
          is_active: is_active,
        }
      );

      if (response.status === 200) {
        message.success("Modifier Group Status updated successfully.");
        getModifierGroups();
        return true;
      } else {
        message.error("Failed to update category.");
        return false;
      }
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        message.error(
          err.response?.data?.message ||
            "An error occurred while updating category."
        );
      } else {
        message.error("An unexpected error occurred.");
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (modifierGroup_id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Modifier Group" : "Deactivate Modifier Group",
      content: isActive
        ? "Are you sure you want to activate this Modifier Group?"
        : "Are you sure you want to deactivate this Modifier Group?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        await updateStoreCategory(modifierGroup_id, isActive);
      },
    });
  };

  const handleSearchChange = useCallback((value: string) => {
    //console.log(value);
    handleFilterChange("search", value);
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearch("");
    handleFilterChange("search", "");
  }, []);

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: "10%",
      fixed: "left" as "left",
      render: (text: string, record: ModifierGroup) => (
        // <span
        //   onClick={() =>
        //     navigate(
        //       `/modifiers-groups-details/${record.id}` +
        //         `?modifiername=${encodeURIComponent(record.name)}`
        //     )
        //   }
        // >
        //   <Link>{text}</Link>
        // </span>
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={
                `/modifiers-groups-details/${record.id}` +
                `?modifiername=${encodeURIComponent(record.name)}`
              }
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
    },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
      width: "20%",
    },
    // {
    //   title: "Sections",
    //   dataIndex: "sections",
    //   key: "sections",
    //   width: "15%",
    // },
    // {
    //   title: "Category",
    //   dataIndex: "category",
    //   key: "category",
    //   width: "15%",
    // },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean, record: ModifierGroup) => (
        <div className="d-flex">
          <div
            className={`switch-button ${isActive ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !isActive)}
          >
            <span className="switch-label">
              {isActive ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <Link to={`./addmodifiergroup`}>
          <Button
            className="typography"
            // onClick={() => navigate("./addmodifiergroup")}
          >
            + Add New
          </Button>
        </Link>
      </div>
      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">MODIFIER GROUPS</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              {/* <input
                  className="search-text"
                  placeholder="Code or Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getModifierGroups(currentPage, search)}>
                  Search
                </button> */}
              <div>
                <SearchBox
                  value={search}
                  onChange={setSearch}
                  onSearch={() => handleSearchChange(search)}
                  onClear={handleClearSearch}
                  placeholder="Enter Name"
                  // className="ms-2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="">
        <DataTable<ModifierGroup>
          columns={columns}
          dataSource={modifierGroups?.objects || []}
          loading={loading}
          rowKey={(record) => String(record.id)}
          pagination={{
            current: currentPage,
            total: totalCount,
            pageSize: pageSize,

            showSizeChanger: true,
            onShowSizeChange: handlePageChange,
            onChange: handlePageChange,
          }}
          scroll={{ x: 1100, y: 700 }}
        />
      </div>
    </div>
  );
};

export default ModifierGroups;
