import axios from "axios";
import { NavigateFunction } from "react-router-dom";

const DEBUG = import.meta.env.MODE === "development";

export const handleApiError = (
  error: unknown,
  setError: (message: string | null) => void,
  navigate: NavigateFunction
) => {
  if (axios.isCancel(error)) {
    DEBUG && console.warn("Request canceled:", (error as any).message);
    return;
  }

  if (!navigator.onLine) {
    setError("No internet connection. Please check your network.");
    return;
  }

  if (axios.isAxiosError(error) && error.response) {
    const { status, data } = error.response;

    switch (status) {
      case 401:
        navigate("/error401");
        break;
      case 403:
        navigate("/error403");
        break;
      case 404:
        navigate("/error404");
        break;
      case 500:
        navigate("/error500");
        break;
      default:
        setError(data?.message || "An unexpected error occurred.");
        break;
    }

    DEBUG && console.error(`API Error [${status}]:`, data);
    return;
  }

  if (axios.isAxiosError(error) && error.request) {
    setError("Network error. Unable to reach the server. Please try again.");
    DEBUG && console.error("Network Error:", error.request);
    return;
  }

  setError((error as any)?.message || "An unexpected error occurred.");
  DEBUG && console.error("Unknown Error:", error);
};
