import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import Cookies from "js-cookie";
import { PrivateRouteProps } from "../types/PrivateRouteProps";

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const token = Cookies.get("token");

  const location = useLocation();

  if (!token) {
    // Prevent infinite loop by using `state` to preserve location
    return <Navigate to="/" state={{ from: location }} replace />;
  }
  return children; // Render children (protected routes) if authenticated
};

export default PrivateRoute;
