import React from "react";
import { Form, Input, Input<PERSON><PERSON><PERSON>, Button } from "antd";
import "../addStore/AddStore.css";

interface AddStoreProps {
  name: string;
  code: string;
  phone: string;
  postal_code: string;
  address: string;
  latitude: number;
  longitude: number;
  tax_percentage: number;
  third_party_id: string;
  coverage_type: string;
  business: number;
  ato_id: string;
}

interface StoreFormProps {
  form: any;
  initialValues: AddStoreProps;
  onFinish: () => void;
  onValuesChange: (_: Partial<AddStoreProps>, allValues: AddStoreProps) => void;
  isEdit?: boolean;
}

const StoreForm: React.FC<StoreFormProps> = ({
  form,
  initialValues,
  onFinish,
  onValuesChange,
  isEdit = false,
}) => {
  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      onValuesChange={onValuesChange}
      initialValues={initialValues}
      className="space-y-4"
    >
      <Form.Item
        name="name"
        label="Store Name"
        rules={[{ required: true, message: "Please enter store name" }]}
      >
        <Input />
      </Form.Item>
      {!isEdit && (
        <Form.Item
          name="code"
          label="Store Code"
          rules={[{ required: true, message: "Please enter store code" }]}
        >
          <Input />
        </Form.Item>
      )}

      <Form.Item
        name="ato_id"
        label="POS Number"
        rules={[{ required: true, message: "Please enter POS number" }]}
      >
        <Input maxLength={6} />
      </Form.Item>
      <Form.Item
        name="phone"
        label="Phone Number"
        rules={[
          { required: true, message: "Please enter phone number" },
          {
            pattern: /^\d{10}$/,
            message: "Phone number must be exactly 10 digits",
          },
        ]}
      >
        <Input maxLength={10} />
      </Form.Item>
      <Form.Item
        name="postal_code"
        label="Postal Code"
        rules={[
          { required: true, message: "Please enter postal code" },
          {
            pattern: /^\d{6}$/,
            message: "Postal code must be exactly 6 digits",
          },
        ]}
      >
        <Input maxLength={6} />
      </Form.Item>
      <Form.Item
        name="address"
        label="Address"
        rules={[{ required: true, message: "Please enter address" }]}
      >
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item
        name="latitude"
        label="Latitude"
        rules={[
          { required: true, message: "Please enter latitude" },
          {
            type: "number",
            min: -90,
            max: 90,
            message: "Latitude must be between -90 and 90",
          },
        ]}
      >
        <InputNumber step={0.000001} style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item
        name="longitude"
        label="Longitude"
        rules={[
          { required: true, message: "Please enter longitude" },
          {
            type: "number",
            min: -180,
            max: 180,
            message: "Longitude must be between -180 and 180",
          },
        ]}
      >
        <InputNumber step={0.000001} style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item
        name="tax_percentage"
        label="Tax Percentage"
        rules={[
          { required: true, message: "Please enter tax percentage" },
          {
            type: "number",
            min: 0,
            max: 100,
            message: "Tax percentage must be between 0 and 100",
          },
        ]}
      >
        <InputNumber min={0} max={100} step={0.01} style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item
        name="third_party_id"
        label="Third Party ID"
        rules={[
          { required: true, message: "Please enter Third Party ID" },
          {
            pattern:
              /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/,
            message: "Please enter a valid UUID v4",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item>
        <Button
          htmlType="submit"
          className="bg-primary hover:bg-primaryHover text-white px-4 py-2 rounded"
        >
          {isEdit ? "Update" : "Submit"}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default StoreForm;
